'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Edit } from 'lucide-react'
import { parcoCaviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { ParcoCavo } from '@/types'
import { canModifyReel } from '@/utils/bobineUtils'

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface FormData {
  numero_bobina: string
  utility: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: string
  metri_residui: string
  ubicazione_bobina: string
  fornitore: string
  n_DDT: string
  data_DDT: string
  configurazione: string
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  bobina,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere } = useAuth()
  const [formData, setFormData] = useState<FormData>({
    numero_bobina: '',
    utility: '',
    tipologia: '',
    n_conduttori: '',
    sezione: '',
    metri_totali: '',
    metri_residui: '',
    ubicazione_bobina: '',
    fornitore: '',
    n_DDT: '',
    data_DDT: '',
    configurazione: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Popola il form quando si apre il dialog con una bobina
  useEffect(() => {
    if (open && bobina) {
      setFormData({
        numero_bobina: bobina.numero_bobina || '',
        utility: bobina.utility || '',
        tipologia: bobina.tipologia || '',
        n_conduttori: bobina.n_conduttori || '',
        sezione: bobina.sezione || '',
        metri_totali: bobina.metri_totali?.toString() || '',
        metri_residui: bobina.metri_residui?.toString() || '',
        ubicazione_bobina: bobina.ubicazione_bobina || '',
        fornitore: bobina.fornitore || '',
        n_DDT: bobina.n_DDT || '',
        data_DDT: bobina.data_DDT || '',
        configurazione: bobina.configurazione || 's'
      })
      setError('')
    }
  }, [open, bobina])

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    setError('')
  }

  const validateForm = (): string | null => {
    if (!formData.numero_bobina.trim()) {
      return 'Il numero bobina è obbligatorio'
    }
    if (!formData.utility.trim()) {
      return 'La utility è obbligatoria'
    }
    if (!formData.tipologia.trim()) {
      return 'La tipologia è obbligatoria'
    }
    if (!formData.sezione.trim()) {
      return 'La sezione è obbligatoria'
    }
    if (!formData.metri_totali.trim()) {
      return 'I metri totali sono obbligatori'
    }
    if (!formData.metri_residui.trim()) {
      return 'I metri residui sono obbligatori'
    }
    
    const metriTotali = parseFloat(formData.metri_totali)
    const metriResidui = parseFloat(formData.metri_residui)
    
    if (isNaN(metriTotali) || metriTotali <= 0) {
      return 'I metri totali devono essere un numero positivo'
    }
    
    if (isNaN(metriResidui)) {
      return 'I metri residui devono essere un numero valido'
    }

    if (metriResidui > metriTotali) {
      return 'I metri residui non possono essere maggiori dei metri totali'
    }

    return null
  }

  const handleSave = async () => {
    if (!bobina) return

    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Prepara i dati per l'API
      const updateData = {
        numero_bobina: formData.numero_bobina.trim(),
        utility: formData.utility.trim().toUpperCase(),
        tipologia: formData.tipologia.trim().toUpperCase(),
        n_conduttori: formData.n_conduttori,
        sezione: formData.sezione.trim(),
        metri_totali: parseFloat(formData.metri_totali),
        metri_residui: parseFloat(formData.metri_residui),
        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',
        fornitore: formData.fornitore.trim() || 'TBD',
        n_DDT: formData.n_DDT.trim() || 'TBD',
        data_DDT: formData.data_DDT || null,
        configurazione: formData.configurazione
      }

      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})
      const bobinaNumero = bobina.id_bobina.split('_B')[1]
      
      await parcoCaviApi.updateBobina(cantiere.id_cantiere, bobinaNumero, updateData)

      onSuccess(`Bobina ${formData.numero_bobina} modificata con successo`)
      onClose()
    } catch (error: any) {
      console.error('Errore nella modifica bobina:', error)
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setError('')
      onClose()
    }
  }

  if (!bobina) return null

  // Verifica se la bobina può essere modificata
  const canModify = canModifyReel(bobina.stato_bobina)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Modifica Bobina {bobina.numero_bobina}
          </DialogTitle>
          <DialogDescription>
            Modifica i dati della bobina {bobina.id_bobina}
            {!canModify && (
              <span className="block text-red-600 mt-1">
                ⚠️ Attenzione: questa bobina è in stato {bobina.stato_bobina} e potrebbe avere limitazioni
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Numero Bobina */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="numero_bobina" className="text-right">
              Numero Bobina *
            </Label>
            <Input
              id="numero_bobina"
              value={formData.numero_bobina}
              onChange={(e) => handleInputChange('numero_bobina', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Utility */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="utility" className="text-right">
              Utility *
            </Label>
            <Input
              id="utility"
              value={formData.utility}
              onChange={(e) => handleInputChange('utility', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Tipologia */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="tipologia" className="text-right">
              Tipologia *
            </Label>
            <Input
              id="tipologia"
              value={formData.tipologia}
              onChange={(e) => handleInputChange('tipologia', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Numero Conduttori */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="n_conduttori" className="text-right">
              N° Conduttori
            </Label>
            <Input
              id="n_conduttori"
              value={formData.n_conduttori}
              onChange={(e) => handleInputChange('n_conduttori', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Sezione */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sezione" className="text-right">
              Sezione *
            </Label>
            <Input
              id="sezione"
              value={formData.sezione}
              onChange={(e) => handleInputChange('sezione', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Metri Totali */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="metri_totali" className="text-right">
              Metri Totali *
            </Label>
            <Input
              id="metri_totali"
              type="number"
              step="0.1"
              min="0"
              value={formData.metri_totali}
              onChange={(e) => handleInputChange('metri_totali', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Metri Residui */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="metri_residui" className="text-right">
              Metri Residui *
            </Label>
            <Input
              id="metri_residui"
              type="number"
              step="0.1"
              value={formData.metri_residui}
              onChange={(e) => handleInputChange('metri_residui', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Ubicazione */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="ubicazione_bobina" className="text-right">
              Ubicazione
            </Label>
            <Input
              id="ubicazione_bobina"
              value={formData.ubicazione_bobina}
              onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Fornitore */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="fornitore" className="text-right">
              Fornitore
            </Label>
            <Input
              id="fornitore"
              value={formData.fornitore}
              onChange={(e) => handleInputChange('fornitore', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Numero DDT */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="n_DDT" className="text-right">
              N° DDT
            </Label>
            <Input
              id="n_DDT"
              value={formData.n_DDT}
              onChange={(e) => handleInputChange('n_DDT', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Data DDT */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="data_DDT" className="text-right">
              Data DDT
            </Label>
            <Input
              id="data_DDT"
              type="date"
              value={formData.data_DDT}
              onChange={(e) => handleInputChange('data_DDT', e.target.value)}
              className="col-span-3"
              disabled={loading}
            />
          </div>

          {/* Configurazione */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="configurazione" className="text-right">
              Configurazione
            </Label>
            <Select
              value={formData.configurazione}
              onValueChange={(value) => handleInputChange('configurazione', value)}
              disabled={loading}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="s">Standard (s)</SelectItem>
                <SelectItem value="m">Manuale (m)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Errori */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Salvando...' : 'Salva Modifiche'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
