'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Edit } from 'lucide-react'
import { parcoCaviApi } from '@/lib/api'

import { ParcoCavo } from '@/types'
import { canModifyReel } from '@/utils/bobineUtils'

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  cantiereId: number
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface FormData {
  numero_bobina: string
  utility: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: string
  metri_residui: string
  ubicazione_bobina: string
  fornitore: string
  n_DDT: string
  data_DDT: string
  configurazione: string
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  bobina,
  cantiereId,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const [formData, setFormData] = useState<FormData>({
    numero_bobina: '',
    utility: '',
    tipologia: '',
    n_conduttori: '',
    sezione: '',
    metri_totali: '',
    metri_residui: '',
    ubicazione_bobina: '',
    fornitore: '',
    n_DDT: '',
    data_DDT: '',
    configurazione: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Popola il form quando si apre il dialog con una bobina
  useEffect(() => {
    if (open && bobina) {
      setFormData({
        numero_bobina: bobina.numero_bobina || '',
        utility: bobina.utility || '',
        tipologia: bobina.tipologia || '',
        n_conduttori: bobina.n_conduttori || '',
        sezione: bobina.sezione || '',
        metri_totali: bobina.metri_totali?.toString() || '',
        metri_residui: bobina.metri_residui?.toString() || '',
        ubicazione_bobina: bobina.ubicazione_bobina || '',
        fornitore: bobina.fornitore || '',
        n_DDT: bobina.n_DDT || '',
        data_DDT: bobina.data_DDT || '',
        configurazione: bobina.configurazione || 's'
      })
      setError('')
    }
  }, [open, bobina])

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    setError('')
  }

  const validateForm = (): string | null => {
    if (!formData.utility.trim()) {
      return 'La utility è obbligatoria'
    }
    if (!formData.tipologia.trim()) {
      return 'La tipologia è obbligatoria'
    }
    if (!formData.sezione.trim()) {
      return 'La formazione è obbligatoria'
    }
    if (!formData.metri_totali.trim()) {
      return 'I metri totali sono obbligatori'
    }
    if (!formData.metri_residui.trim()) {
      return 'I metri residui sono obbligatori'
    }
    
    const metriTotali = parseFloat(formData.metri_totali)
    const metriResidui = parseFloat(formData.metri_residui)
    
    if (isNaN(metriTotali) || metriTotali <= 0) {
      return 'I metri totali devono essere un numero positivo'
    }
    
    if (isNaN(metriResidui)) {
      return 'I metri residui devono essere un numero valido'
    }

    if (metriResidui > metriTotali) {
      return 'I metri residui non possono essere maggiori dei metri totali'
    }

    return null
  }

  const handleSave = async () => {
    if (!bobina) return

    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiereId || cantiereId <= 0) {
        throw new Error('Cantiere non selezionato')
      }

      // Verifica se la bobina può essere modificata completamente
      const canModifyFully = canModifyReel(bobina.stato_bobina)

      // Prepara i dati per l'API
      const updateData: any = {
        // Campi sempre modificabili
        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',
        fornitore: formData.fornitore.trim() || 'TBD',
        n_DDT: formData.n_DDT.trim() || 'TBD',
        data_DDT: formData.data_DDT || null,
      }

      // Campi modificabili solo se bobina è disponibile
      if (canModifyFully) {
        updateData.utility = formData.utility.trim().toUpperCase()
        updateData.tipologia = formData.tipologia.trim().toUpperCase()
        updateData.sezione = formData.sezione.trim()
        updateData.metri_totali = parseFloat(formData.metri_totali)
        updateData.n_conduttori = '0' // Campo disponibile, sempre impostato a '0'
        updateData.configurazione = formData.configurazione
      }

      // NOTA: metri_residui è un campo di sistema e non viene mai inviato

      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})
      const bobinaNumero = bobina.id_bobina.split('_B')[1]
      
      await parcoCaviApi.updateBobina(cantiereId, bobinaNumero, updateData)

      onSuccess(`Bobina ${formData.numero_bobina} modificata con successo`)
      onClose()
    } catch (error: any) {
      console.error('Errore nella modifica bobina:', error)
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setError('')
      onClose()
    }
  }

  if (!bobina) return null

  // Verifica se la bobina può essere modificata
  const canModify = canModifyReel(bobina.stato_bobina)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Modifica Bobina {bobina.numero_bobina}
          </DialogTitle>
          <DialogDescription>
            Modifica bobina {bobina.numero_bobina}
          </DialogDescription>
        </DialogHeader>

        {/* Avviso limitazioni modifica */}
        {!canModify && (
          <div className="mx-6 mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
            <div className="text-amber-800 text-sm">
              <strong>⚠️ Limitazioni di modifica:</strong>
              <br />
              La bobina è in stato "{bobina.stato_bobina}".
              <br />
              Puoi modificare solo: <strong>Ubicazione, Fornitore, N° DDT, Data DDT</strong>
            </div>
          </div>
        )}

        <div className="grid gap-6 py-6">
          {/* Colonna sinistra */}
          <div className="space-y-4">
            {/* Utility */}
            <div className="space-y-2">
              <Label htmlFor="utility">Utility *</Label>
              <Input
                id="utility"
                value={formData.utility}
                onChange={(e) => handleInputChange('utility', e.target.value)}
                disabled={loading || !canModify}
                title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}
              />
            </div>

            {/* Tipologia */}
            <div className="space-y-2">
              <Label htmlFor="tipologia">Tipologia *</Label>
              <Input
                id="tipologia"
                value={formData.tipologia}
                onChange={(e) => handleInputChange('tipologia', e.target.value)}
                disabled={loading || !canModify}
                title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}
              />
            </div>

            {/* Formazione */}
            <div className="space-y-2">
              <Label htmlFor="sezione">Formazione *</Label>
              <Input
                id="sezione"
                value={formData.sezione}
                onChange={(e) => handleInputChange('sezione', e.target.value)}
                disabled={loading || !canModify}
                title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}
              />
            </div>

            {/* Metri Totali */}
            <div className="space-y-2">
              <Label htmlFor="metri_totali">Metri Totali *</Label>
              <Input
                id="metri_totali"
                type="number"
                step="0.1"
                min="0"
                value={formData.metri_totali}
                onChange={(e) => handleInputChange('metri_totali', e.target.value)}
                disabled={loading || !canModify}
                title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}
              />
            </div>
          </div>

          {/* Colonna destra */}
          <div className="space-y-4">
            {/* Ubicazione */}
            <div className="space-y-2">
              <Label htmlFor="ubicazione_bobina">Ubicazione</Label>
              <Input
                id="ubicazione_bobina"
                value={formData.ubicazione_bobina}
                onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Fornitore */}
            <div className="space-y-2">
              <Label htmlFor="fornitore">Fornitore</Label>
              <Input
                id="fornitore"
                value={formData.fornitore}
                onChange={(e) => handleInputChange('fornitore', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Numero DDT */}
            <div className="space-y-2">
              <Label htmlFor="n_DDT">N° DDT</Label>
              <Input
                id="n_DDT"
                value={formData.n_DDT}
                onChange={(e) => handleInputChange('n_DDT', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Data DDT */}
            <div className="space-y-2">
              <Label htmlFor="data_DDT">Data DDT</Label>
              <Input
                id="data_DDT"
                type="date"
                value={formData.data_DDT}
                onChange={(e) => handleInputChange('data_DDT', e.target.value)}
                disabled={loading}
              />
            </div>
          </div>
        </div>

        {/* Errori */}
        {error && (
          <div className="mx-6 mb-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Salvando...' : 'Salva Modifiche'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
