{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/bobineUtils.ts"], "sourcesContent": ["/**\n * Utility per la gestione degli stati delle bobine\n * Implementa le stesse regole della webapp originale\n */\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n} as const\n\nexport type ReelState = typeof REEL_STATES[keyof typeof REEL_STATES]\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Stato della bobina\n */\nexport const determineReelState = (metriResidui: number, metriTotali: number): ReelState => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER\n  }\n\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA\n  }\n\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO\n  }\n\n  return REEL_STATES.DISPONIBILE\n}\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param statoBobina - Stato della bobina\n * @returns True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = (statoBobina: string): boolean => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. È in stato IN_USO\n  // 3. Non è in stato TERMINATA o OVER\n  return statoBobina === REEL_STATES.DISPONIBILE || \n         statoBobina === REEL_STATES.IN_USO\n}\n\n/**\n * Ottiene il colore associato a uno stato della bobina per i badge\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore del badge\n */\nexport const getReelStateColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'bg-green-100 text-green-800'\n    case REEL_STATES.IN_USO:\n      return 'bg-yellow-100 text-yellow-800'\n    case REEL_STATES.TERMINATA:\n      return 'bg-red-100 text-red-800'\n    case REEL_STATES.OVER:\n      return 'bg-red-500 text-white'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\n/**\n * Ottiene il colore di sfondo per le righe della tabella in base allo stato\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore di sfondo\n */\nexport const getReelRowColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'hover:bg-green-50'\n    case REEL_STATES.IN_USO:\n      return 'hover:bg-yellow-50'\n    case REEL_STATES.TERMINATA:\n      return 'hover:bg-red-50'\n    case REEL_STATES.OVER:\n      return 'hover:bg-red-100'\n    default:\n      return 'hover:bg-gray-50'\n  }\n}\n\n/**\n * Verifica se una bobina è utilizzabile per nuove installazioni\n * @param statoBobina - Stato della bobina\n * @param metriResidui - Metri residui\n * @returns True se la bobina è utilizzabile\n */\nexport const isReelUsable = (statoBobina: string, metriResidui: number): boolean => {\n  return (statoBobina === REEL_STATES.DISPONIBILE || statoBobina === REEL_STATES.IN_USO) && \n         metriResidui > 0\n}\n\n/**\n * Calcola la percentuale di utilizzo di una bobina\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Percentuale di utilizzo (0-100)\n */\nexport const calculateReelUsagePercentage = (metriResidui: number, metriTotali: number): number => {\n  if (metriTotali <= 0) return 0\n  const utilizzo = ((metriTotali - metriResidui) / metriTotali) * 100\n  return Math.max(0, Math.min(100, utilizzo))\n}\n\n/**\n * Formatta i metri per la visualizzazione\n * @param metri - Metri da formattare\n * @returns Stringa formattata con unità\n */\nexport const formatMeters = (metri: number): string => {\n  return `${metri.toFixed(1)}m`\n}\n\n/**\n * Ottiene una descrizione testuale dello stato della bobina\n * @param stato - Stato della bobina\n * @returns Descrizione dello stato\n */\nexport const getReelStateDescription = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'Bobina disponibile per nuove installazioni'\n    case REEL_STATES.IN_USO:\n      return 'Bobina parzialmente utilizzata'\n    case REEL_STATES.TERMINATA:\n      return 'Bobina completamente esaurita'\n    case REEL_STATES.OVER:\n      return 'Bobina sovra-utilizzata (metri negativi)'\n    default:\n      return 'Stato non definito'\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,qBAAqB;;;;;;;;;;;;AACd,MAAM,cAAc;IACzB,aAAa;IACb,QAAQ;IACR,WAAW;IACX,MAAM;AACR;AAUO,MAAM,qBAAqB,CAAC,cAAsB;IACvD,IAAI,eAAe,GAAG;QACpB,OAAO,YAAY,IAAI;IACzB;IAEA,IAAI,iBAAiB,GAAG;QACtB,OAAO,YAAY,SAAS;IAC9B;IAEA,IAAI,eAAe,aAAa;QAC9B,OAAO,YAAY,MAAM;IAC3B;IAEA,OAAO,YAAY,WAAW;AAChC;AAOO,MAAM,gBAAgB,CAAC;IAC5B,uCAAuC;IACvC,4BAA4B;IAC5B,uBAAuB;IACvB,qCAAqC;IACrC,OAAO,gBAAgB,YAAY,WAAW,IACvC,gBAAgB,YAAY,MAAM;AAC3C;AAOO,MAAM,oBAAoB,CAAC;IAChC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAOO,MAAM,kBAAkB,CAAC;IAC9B,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAQO,MAAM,eAAe,CAAC,aAAqB;IAChD,OAAO,CAAC,gBAAgB,YAAY,WAAW,IAAI,gBAAgB,YAAY,MAAM,KAC9E,eAAe;AACxB;AAQO,MAAM,+BAA+B,CAAC,cAAsB;IACjE,IAAI,eAAe,GAAG,OAAO;IAC7B,MAAM,WAAW,AAAC,CAAC,cAAc,YAAY,IAAI,cAAe;IAChE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;AACnC;AAOO,MAAM,eAAe,CAAC;IAC3B,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAOO,MAAM,0BAA0B,CAAC;IACtC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/CreaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Plus } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\nconst initialFormData: FormData = {\n  numero_bobina: '',\n  utility: '',\n  tipologia: '',\n  n_conduttori: '0',\n  sezione: '',\n  metri_totali: '',\n  ubicazione_bobina: 'TBD',\n  fornitore: 'TBD',\n  n_DDT: 'TBD',\n  data_DDT: '',\n  configurazione: 's'\n}\n\nexport default function CreaBobinaDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: CreaBobinaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState<FormData>(initialFormData)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [nextBobinaNumber, setNextBobinaNumber] = useState('1')\n  const [loadingConfig, setLoadingConfig] = useState(false)\n  const [isFirstInsertion, setIsFirstInsertion] = useState(true)\n  const [configurazioneFixed, setConfigurazioneFixed] = useState('')\n  const [showConfigSelection, setShowConfigSelection] = useState(false)\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere) {\n      setFormData(initialFormData)\n      setError('')\n      checkFirstInsertion()\n    }\n  }, [open, cantiere])\n\n  // Verifica se è il primo inserimento e carica la configurazione\n  const checkFirstInsertion = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingConfig(true)\n      const response = await parcoCaviApi.isFirstBobinaInsertion(cantiere.id_cantiere)\n\n      setIsFirstInsertion(response.is_first_insertion)\n\n      if (response.is_first_insertion) {\n        // Primo inserimento: mostra selezione configurazione\n        setShowConfigSelection(true)\n        setConfigurazioneFixed('')\n      } else {\n        // Non è il primo inserimento: usa configurazione esistente\n        setConfigurazioneFixed(response.configurazione)\n        setFormData(prev => ({\n          ...prev,\n          configurazione: response.configurazione\n        }))\n        setShowConfigSelection(false)\n\n        // Carica il prossimo numero se configurazione è standard\n        if (response.configurazione === 's') {\n          await loadNextBobinaNumber()\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel controllo primo inserimento:', error)\n      // In caso di errore, assume primo inserimento\n      setIsFirstInsertion(true)\n      setShowConfigSelection(true)\n    } finally {\n      setLoadingConfig(false)\n    }\n  }\n\n  // Funzione per caricare il prossimo numero bobina (numerazione globale)\n  const loadNextBobinaNumber = async () => {\n    if (!cantiere) return\n\n    try {\n      // Per la numerazione globale, recupera TUTTE le bobine di TUTTI i cantieri\n      // e trova il numero massimo globale\n      const bobine = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      if (bobine && bobine.length > 0) {\n        // Filtra solo le bobine con numero_bobina numerico\n        const numericBobine = bobine.filter(b =>\n          b.numero_bobina && /^\\d+$/.test(b.numero_bobina)\n        )\n\n        if (numericBobine.length > 0) {\n          // Trova il numero massimo tra le bobine esistenti\n          const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)))\n          const nextNumber = String(maxNumber + 1)\n          setNextBobinaNumber(nextNumber)\n\n          // Se la configurazione è standard, imposta automaticamente il numero\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: nextNumber\n          }))\n        } else {\n          setNextBobinaNumber('1')\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: '1'\n          }))\n        }\n      } else {\n        setNextBobinaNumber('1')\n        setFormData(prev => ({\n          ...prev,\n          numero_bobina: '1'\n        }))\n      }\n    } catch (error) {\n      console.error('Errore nel recupero del prossimo numero bobina:', error)\n      setNextBobinaNumber('1')\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: '1'\n      }))\n    }\n  }\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError('')\n  }\n\n  // Gestisce la selezione della configurazione (solo al primo inserimento)\n  const handleConfigSelection = async (config: string) => {\n    setConfigurazioneFixed(config)\n    setFormData(prev => ({\n      ...prev,\n      configurazione: config\n    }))\n    setShowConfigSelection(false)\n\n    // Se configurazione standard, carica il prossimo numero\n    if (config === 's') {\n      await loadNextBobinaNumber()\n    } else {\n      // Configurazione manuale: svuota il campo\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: ''\n      }))\n    }\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.numero_bobina.trim()) {\n      return 'Il numero bobina è obbligatorio'\n    }\n\n    // Validazione per configurazione manuale\n    if (formData.configurazione === 'm') {\n      const numeroInput = formData.numero_bobina.trim()\n\n      // Verifica caratteri non consentiti\n      if (/[\\s\\\\/:*?\"<>|]/.test(numeroInput)) {\n        return 'Il numero bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \" < > |'\n      }\n    }\n\n    if (!formData.utility.trim()) {\n      return 'La utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      return 'La tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      return 'La sezione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      return 'I metri totali sono obbligatori'\n    }\n\n    const metri = parseFloat(formData.metri_totali)\n    if (isNaN(metri) || metri <= 0) {\n      return 'I metri totali devono essere un numero positivo'\n    }\n\n    return null\n  }\n\n  const handleSave = async () => {\n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Prepara i dati per l'API\n      const bobinaData = {\n        numero_bobina: formData.numero_bobina.trim(),\n        utility: formData.utility.trim().toUpperCase(),\n        tipologia: formData.tipologia.trim().toUpperCase(),\n        n_conduttori: formData.n_conduttori,\n        sezione: formData.sezione.trim(),\n        metri_totali: parseFloat(formData.metri_totali),\n        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',\n        fornitore: formData.fornitore.trim() || 'TBD',\n        n_DDT: formData.n_DDT.trim() || 'TBD',\n        data_DDT: formData.data_DDT || null,\n        configurazione: formData.configurazione\n      }\n\n      await parcoCaviApi.createBobina(cantiere.id_cantiere, bobinaData)\n\n      onSuccess(`Bobina ${formData.numero_bobina} creata con successo`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella creazione bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFormData(initialFormData)\n      setError('')\n      onClose()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Plus className=\"h-5 w-5\" />\n            Crea Nuova Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Inserisci i dati per creare una nuova bobina nel parco cavi\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n          {/* Configurazione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"configurazione\" className=\"text-right\">\n              Configurazione\n            </Label>\n            <Select\n              value={formData.configurazione}\n              onValueChange={(value) => handleInputChange('configurazione', value)}\n              disabled={loading}\n            >\n              <SelectTrigger className=\"col-span-3\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"s\">Standard (s) - Numerazione automatica</SelectItem>\n                <SelectItem value=\"m\">Manuale (m) - Inserimento manuale</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Numero Bobina */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"numero_bobina\" className=\"text-right\">\n              Numero Bobina *\n            </Label>\n            <div className=\"col-span-3 space-y-2\">\n              <Input\n                id=\"numero_bobina\"\n                value={formData.numero_bobina}\n                onChange={(e) => handleInputChange('numero_bobina', e.target.value)}\n                placeholder={formData.configurazione === 's' ? 'Generato automaticamente' : 'Es: A123, TEST01'}\n                disabled={loading || formData.configurazione === 's'}\n                className={formData.configurazione === 's' ? 'bg-gray-50' : ''}\n              />\n              <div className=\"text-sm text-gray-600\">\n                {formData.configurazione === 's' ? (\n                  <span>\n                    ID completo: <strong>C{cantiere?.id_cantiere}_B{formData.numero_bobina || nextBobinaNumber}</strong> (generato automaticamente)\n                    {loadingNextNumber && <span className=\"ml-2\">Caricamento...</span>}\n                  </span>\n                ) : (\n                  <span>\n                    Inserisci il numero della bobina. ID completo sarà: <strong>C{cantiere?.id_cantiere}_B{formData.numero_bobina || '{numero}'}</strong>\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Utility */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"utility\" className=\"text-right\">\n              Utility *\n            </Label>\n            <Input\n              id=\"utility\"\n              value={formData.utility}\n              onChange={(e) => handleInputChange('utility', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: ENEL, TIM, OPEN FIBER\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Tipologia */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"tipologia\" className=\"text-right\">\n              Tipologia *\n            </Label>\n            <Input\n              id=\"tipologia\"\n              value={formData.tipologia}\n              onChange={(e) => handleInputChange('tipologia', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: FO, RAME\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Numero Conduttori */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"n_conduttori\" className=\"text-right\">\n              N° Conduttori\n            </Label>\n            <Input\n              id=\"n_conduttori\"\n              value={formData.n_conduttori}\n              onChange={(e) => handleInputChange('n_conduttori', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: 12, 24, 48\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Sezione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"sezione\" className=\"text-right\">\n              Sezione *\n            </Label>\n            <Input\n              id=\"sezione\"\n              value={formData.sezione}\n              onChange={(e) => handleInputChange('sezione', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: 9/125, 50/125, 1.5\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Metri Totali */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"metri_totali\" className=\"text-right\">\n              Metri Totali *\n            </Label>\n            <Input\n              id=\"metri_totali\"\n              type=\"number\"\n              step=\"0.1\"\n              min=\"0\"\n              value={formData.metri_totali}\n              onChange={(e) => handleInputChange('metri_totali', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: 1000\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Ubicazione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"ubicazione_bobina\" className=\"text-right\">\n              Ubicazione\n            </Label>\n            <Input\n              id=\"ubicazione_bobina\"\n              value={formData.ubicazione_bobina}\n              onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: Magazzino A, Cantiere\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Fornitore */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"fornitore\" className=\"text-right\">\n              Fornitore\n            </Label>\n            <Input\n              id=\"fornitore\"\n              value={formData.fornitore}\n              onChange={(e) => handleInputChange('fornitore', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: Prysmian, Nexans\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Numero DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"n_DDT\" className=\"text-right\">\n              N° DDT\n            </Label>\n            <Input\n              id=\"n_DDT\"\n              value={formData.n_DDT}\n              onChange={(e) => handleInputChange('n_DDT', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: DDT001\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Data DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"data_DDT\" className=\"text-right\">\n              Data DDT\n            </Label>\n            <Input\n              id=\"data_DDT\"\n              type=\"date\"\n              value={formData.data_DDT}\n              onChange={(e) => handleInputChange('data_DDT', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSave} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Creando...' : 'Crea Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AACA;AACA;AAxBA;;;;;;;;;;;;AA+CA,MAAM,kBAA4B;IAChC,eAAe;IACf,SAAS;IACT,WAAW;IACX,cAAc;IACd,SAAS;IACT,cAAc;IACd,mBAAmB;IACnB,WAAW;IACX,OAAO;IACP,UAAU;IACV,gBAAgB;AAClB;AAEe,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU;YACpB,YAAY;YACZ,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,gEAAgE;IAChE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC,SAAS,WAAW;YAE/E,oBAAoB,SAAS,kBAAkB;YAE/C,IAAI,SAAS,kBAAkB,EAAE;gBAC/B,qDAAqD;gBACrD,uBAAuB;gBACvB,uBAAuB;YACzB,OAAO;gBACL,2DAA2D;gBAC3D,uBAAuB,SAAS,cAAc;gBAC9C,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,gBAAgB,SAAS,cAAc;oBACzC,CAAC;gBACD,uBAAuB;gBAEvB,yDAAyD;gBACzD,IAAI,SAAS,cAAc,KAAK,KAAK;oBACnC,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,8CAA8C;YAC9C,oBAAoB;YACpB,uBAAuB;QACzB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,wEAAwE;IACxE,MAAM,uBAAuB;QAC3B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,2EAA2E;YAC3E,oCAAoC;YACpC,MAAM,SAAS,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAEhE,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;gBAC/B,mDAAmD;gBACnD,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAClC,EAAE,aAAa,IAAI,QAAQ,IAAI,CAAC,EAAE,aAAa;gBAGjD,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,kDAAkD;oBAClD,MAAM,YAAY,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,aAAa,EAAE;oBAC/E,MAAM,aAAa,OAAO,YAAY;oBACtC,oBAAoB;oBAEpB,qEAAqE;oBACrE,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH,OAAO;oBACL,oBAAoB;oBACpB,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH;YACF,OAAO;gBACL,oBAAoB;gBACpB,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,eAAe;oBACjB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,oBAAoB;YACpB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,yEAAyE;IACzE,MAAM,wBAAwB,OAAO;QACnC,uBAAuB;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,gBAAgB;YAClB,CAAC;QACD,uBAAuB;QAEvB,wDAAwD;QACxD,IAAI,WAAW,KAAK;YAClB,MAAM;QACR,OAAO;YACL,0CAA0C;YAC1C,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,OAAO;QACT;QAEA,yCAAyC;QACzC,IAAI,SAAS,cAAc,KAAK,KAAK;YACnC,MAAM,cAAc,SAAS,aAAa,CAAC,IAAI;YAE/C,oCAAoC;YACpC,IAAI,iBAAiB,IAAI,CAAC,cAAc;gBACtC,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAC9C,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,aAAa;gBACjB,eAAe,SAAS,aAAa,CAAC,IAAI;gBAC1C,SAAS,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW;gBAC5C,WAAW,SAAS,SAAS,CAAC,IAAI,GAAG,WAAW;gBAChD,cAAc,SAAS,YAAY;gBACnC,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,cAAc,WAAW,SAAS,YAAY;gBAC9C,mBAAmB,SAAS,iBAAiB,CAAC,IAAI,MAAM;gBACxD,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,gBAAgB,SAAS,cAAc;YACzC;YAEA,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAEtD,UAAU,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,oBAAoB,CAAC;YAChE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,YAAY;YACZ,SAAS;YACT;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;8CAAa;;;;;;8CAGvD,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,cAAc;oCAC9B,eAAe,CAAC,QAAU,kBAAkB,kBAAkB;oCAC9D,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAgB,WAAU;8CAAa;;;;;;8CAGtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,aAAa;4CAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAClE,aAAa,SAAS,cAAc,KAAK,MAAM,6BAA6B;4CAC5E,UAAU,WAAW,SAAS,cAAc,KAAK;4CACjD,WAAW,SAAS,cAAc,KAAK,MAAM,eAAe;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;sDACZ,SAAS,cAAc,KAAK,oBAC3B,8OAAC;;oDAAK;kEACS,8OAAC;;4DAAO;4DAAE,UAAU;4DAAY;4DAAG,SAAS,aAAa,IAAI;;;;;;;oDAA0B;oDACnG,mCAAqB,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;qEAG/C,8OAAC;;oDAAK;kEACgD,8OAAC;;4DAAO;4DAAE,UAAU;4DAAY;4DAAG,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ3H,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAa;;;;;;8CAGrD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAa;;;;;;8CAGrD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAoB,WAAU;8CAAa;;;;;;8CAG1D,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,iBAAiB;oCACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACtE,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAQ,WAAU;8CAAa;;;;;;8CAG9C,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAa;;;;;;8CAGjD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;oCACV,UAAU;;;;;;;;;;;;wBAOb,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Edit } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { ParcoCavo } from '@/types'\nimport { canModifyReel } from '@/utils/bobineUtils'\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: string\n  metri_residui: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\nexport default function ModificaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState<FormData>({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Popola il form quando si apre il dialog con una bobina\n  useEffect(() => {\n    if (open && bobina) {\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali?.toString() || '',\n        metri_residui: bobina.metri_residui?.toString() || '',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || 's'\n      })\n      setError('')\n    }\n  }, [open, bobina])\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError('')\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.utility.trim()) {\n      return 'La utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      return 'La tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      return 'La sezione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      return 'I metri totali sono obbligatori'\n    }\n    if (!formData.metri_residui.trim()) {\n      return 'I metri residui sono obbligatori'\n    }\n    \n    const metriTotali = parseFloat(formData.metri_totali)\n    const metriResidui = parseFloat(formData.metri_residui)\n    \n    if (isNaN(metriTotali) || metriTotali <= 0) {\n      return 'I metri totali devono essere un numero positivo'\n    }\n    \n    if (isNaN(metriResidui)) {\n      return 'I metri residui devono essere un numero valido'\n    }\n\n    if (metriResidui > metriTotali) {\n      return 'I metri residui non possono essere maggiori dei metri totali'\n    }\n\n    return null\n  }\n\n  const handleSave = async () => {\n    if (!bobina) return\n\n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Prepara i dati per l'API (escluso numero_bobina che non può essere modificato)\n      const updateData = {\n        utility: formData.utility.trim().toUpperCase(),\n        tipologia: formData.tipologia.trim().toUpperCase(),\n        n_conduttori: formData.n_conduttori,\n        sezione: formData.sezione.trim(),\n        metri_totali: parseFloat(formData.metri_totali),\n        metri_residui: parseFloat(formData.metri_residui),\n        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',\n        fornitore: formData.fornitore.trim() || 'TBD',\n        n_DDT: formData.n_DDT.trim() || 'TBD',\n        data_DDT: formData.data_DDT || null,\n        configurazione: formData.configurazione\n      }\n\n      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})\n      const bobinaNumero = bobina.id_bobina.split('_B')[1]\n      \n      await parcoCaviApi.updateBobina(cantiere.id_cantiere, bobinaNumero, updateData)\n\n      onSuccess(`Bobina ${formData.numero_bobina} modificata con successo`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella modifica bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  // Verifica se la bobina può essere modificata\n  const canModify = canModifyReel(bobina.stato_bobina)\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Edit className=\"h-5 w-5\" />\n            Modifica Bobina {bobina.numero_bobina}\n          </DialogTitle>\n          <DialogDescription>\n            Modifica i dati della bobina {bobina.id_bobina}\n            {!canModify && (\n              <span className=\"block text-red-600 mt-1\">\n                ⚠️ Attenzione: questa bobina è in stato {bobina.stato_bobina} e potrebbe avere limitazioni\n              </span>\n            )}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n          {/* ID Bobina (solo visualizzazione) */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label className=\"text-right\">\n              ID Bobina\n            </Label>\n            <div className=\"col-span-3 space-y-2\">\n              <div className=\"px-3 py-2 bg-gray-50 border rounded-md text-sm\">\n                <strong>{bobina.id_bobina}</strong>\n              </div>\n              <div className=\"text-xs text-gray-600\">\n                L'ID bobina non può essere modificato dopo la creazione\n              </div>\n            </div>\n          </div>\n\n          {/* Numero Bobina (solo visualizzazione) */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label className=\"text-right\">\n              Numero Bobina\n            </Label>\n            <div className=\"col-span-3 space-y-2\">\n              <div className=\"px-3 py-2 bg-gray-50 border rounded-md text-sm\">\n                <strong>{formData.numero_bobina}</strong>\n              </div>\n              <div className=\"text-xs text-gray-600\">\n                Configurazione: {formData.configurazione === 's' ? 'Standard (automatica)' : 'Manuale'}\n              </div>\n            </div>\n          </div>\n\n          {/* Utility */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"utility\" className=\"text-right\">\n              Utility *\n            </Label>\n            <Input\n              id=\"utility\"\n              value={formData.utility}\n              onChange={(e) => handleInputChange('utility', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Tipologia */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"tipologia\" className=\"text-right\">\n              Tipologia *\n            </Label>\n            <Input\n              id=\"tipologia\"\n              value={formData.tipologia}\n              onChange={(e) => handleInputChange('tipologia', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Numero Conduttori */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"n_conduttori\" className=\"text-right\">\n              N° Conduttori\n            </Label>\n            <Input\n              id=\"n_conduttori\"\n              value={formData.n_conduttori}\n              onChange={(e) => handleInputChange('n_conduttori', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Sezione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"sezione\" className=\"text-right\">\n              Sezione *\n            </Label>\n            <Input\n              id=\"sezione\"\n              value={formData.sezione}\n              onChange={(e) => handleInputChange('sezione', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Metri Totali */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"metri_totali\" className=\"text-right\">\n              Metri Totali *\n            </Label>\n            <Input\n              id=\"metri_totali\"\n              type=\"number\"\n              step=\"0.1\"\n              min=\"0\"\n              value={formData.metri_totali}\n              onChange={(e) => handleInputChange('metri_totali', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Metri Residui */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"metri_residui\" className=\"text-right\">\n              Metri Residui *\n            </Label>\n            <Input\n              id=\"metri_residui\"\n              type=\"number\"\n              step=\"0.1\"\n              value={formData.metri_residui}\n              onChange={(e) => handleInputChange('metri_residui', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Ubicazione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"ubicazione_bobina\" className=\"text-right\">\n              Ubicazione\n            </Label>\n            <Input\n              id=\"ubicazione_bobina\"\n              value={formData.ubicazione_bobina}\n              onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Fornitore */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"fornitore\" className=\"text-right\">\n              Fornitore\n            </Label>\n            <Input\n              id=\"fornitore\"\n              value={formData.fornitore}\n              onChange={(e) => handleInputChange('fornitore', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Numero DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"n_DDT\" className=\"text-right\">\n              N° DDT\n            </Label>\n            <Input\n              id=\"n_DDT\"\n              value={formData.n_DDT}\n              onChange={(e) => handleInputChange('n_DDT', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Data DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"data_DDT\" className=\"text-right\">\n              Data DDT\n            </Label>\n            <Input\n              id=\"data_DDT\"\n              type=\"date\"\n              value={formData.data_DDT}\n              onChange={(e) => handleInputChange('data_DDT', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSave} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva Modifiche'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AA1BA;;;;;;;;;;;;AAmDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,eAAe;QACf,SAAS;QACT,WAAW;QACX,cAAc;QACd,SAAS;QACT,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,WAAW;QACX,OAAO;QACP,UAAU;QACV,gBAAgB;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,QAAQ;YAClB,YAAY;gBACV,eAAe,OAAO,aAAa,IAAI;gBACvC,SAAS,OAAO,OAAO,IAAI;gBAC3B,WAAW,OAAO,SAAS,IAAI;gBAC/B,cAAc,OAAO,YAAY,IAAI;gBACrC,SAAS,OAAO,OAAO,IAAI;gBAC3B,cAAc,OAAO,YAAY,EAAE,cAAc;gBACjD,eAAe,OAAO,aAAa,EAAE,cAAc;gBACnD,mBAAmB,OAAO,iBAAiB,IAAI;gBAC/C,WAAW,OAAO,SAAS,IAAI;gBAC/B,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU,OAAO,QAAQ,IAAI;gBAC7B,gBAAgB,OAAO,cAAc,IAAI;YAC3C;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO;QACT;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,OAAO;QACT;QAEA,MAAM,cAAc,WAAW,SAAS,YAAY;QACpD,MAAM,eAAe,WAAW,SAAS,aAAa;QAEtD,IAAI,MAAM,gBAAgB,eAAe,GAAG;YAC1C,OAAO;QACT;QAEA,IAAI,MAAM,eAAe;YACvB,OAAO;QACT;QAEA,IAAI,eAAe,aAAa;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,iFAAiF;YACjF,MAAM,aAAa;gBACjB,SAAS,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW;gBAC5C,WAAW,SAAS,SAAS,CAAC,IAAI,GAAG,WAAW;gBAChD,cAAc,SAAS,YAAY;gBACnC,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,cAAc,WAAW,SAAS,YAAY;gBAC9C,eAAe,WAAW,SAAS,aAAa;gBAChD,mBAAmB,SAAS,iBAAiB,CAAC,IAAI,MAAM;gBACxD,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,gBAAgB,SAAS,cAAc;YACzC;YAEA,kEAAkE;YAClE,MAAM,eAAe,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAEpD,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE,cAAc;YAEpE,UAAU,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,wBAAwB,CAAC;YACpE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,8CAA8C;IAC9C,MAAM,YAAY,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,2MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;gCACX,OAAO,aAAa;;;;;;;sCAEvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACa,OAAO,SAAS;gCAC7C,CAAC,2BACA,8OAAC;oCAAK,WAAU;;wCAA0B;wCACC,OAAO,YAAY;wCAAC;;;;;;;;;;;;;;;;;;;8BAMrE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAa;;;;;;8CAG9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAQ,OAAO,SAAS;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAO3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAa;;;;;;8CAG9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAQ,SAAS,aAAa;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;gDAAwB;gDACpB,SAAS,cAAc,KAAK,MAAM,0BAA0B;;;;;;;;;;;;;;;;;;;sCAMnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAa;;;;;;8CAGrD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAa;;;;;;8CAGrD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAgB,WAAU;8CAAa;;;;;;8CAGtD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAClE,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAoB,WAAU;8CAAa;;;;;;8CAG1D,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,iBAAiB;oCACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACtE,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAQ,WAAU;8CAAa;;;;;;8CAG9C,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAa;;;;;;8CAGjD,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;oCACV,UAAU;;;;;;;;;;;;wBAOb,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/EliminaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { <PERSON>ader2, <PERSON>ert<PERSON><PERSON>gle, Trash2 } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { ParcoCavo } from '@/types'\nimport { canModifyReel, getReelStateDescription } from '@/utils/bobineUtils'\n\ninterface EliminaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function EliminaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  onSuccess,\n  onError\n}: EliminaBobinaDialogProps) {\n  const { cantiere } = useAuth()\n  const [loading, setLoading] = useState(false)\n\n  const handleDelete = async () => {\n    if (!bobina) return\n\n    try {\n      setLoading(true)\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})\n      const bobinaNumero = bobina.id_bobina.split('_B')[1]\n      \n      const response = await parcoCaviApi.deleteBobina(cantiere.id_cantiere, bobinaNumero)\n\n      let message = `Bobina ${bobina.numero_bobina} eliminata con successo`\n      \n      // Se è l'ultima bobina, aggiungi informazione aggiuntiva\n      if (response.data?.is_last_bobina) {\n        message += '. Era l\\'ultima bobina del cantiere.'\n      }\n\n      onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nell\\'eliminazione bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'eliminazione della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  // Verifica se la bobina può essere eliminata\n  const canDelete = canModifyReel(bobina.stato_bobina) && \n                   bobina.metri_residui === bobina.metri_totali\n\n  // Determina il tipo di avviso da mostrare\n  const getWarningInfo = () => {\n    if (!canModifyReel(bobina.stato_bobina)) {\n      return {\n        type: 'error' as const,\n        title: 'Eliminazione non consentita',\n        message: `La bobina è in stato \"${bobina.stato_bobina}\" e non può essere eliminata. ${getReelStateDescription(bobina.stato_bobina)}`\n      }\n    }\n\n    if (bobina.metri_residui !== bobina.metri_totali) {\n      return {\n        type: 'error' as const,\n        title: 'Bobina in uso',\n        message: `La bobina ha ${bobina.metri_residui}m residui su ${bobina.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`\n      }\n    }\n\n    return {\n      type: 'warning' as const,\n      title: 'Conferma eliminazione',\n      message: 'Questa operazione è irreversibile. La bobina verrà rimossa definitivamente dal parco cavi.'\n    }\n  }\n\n  const warningInfo = getWarningInfo()\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Trash2 className=\"h-5 w-5 text-red-600\" />\n            Elimina Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Stai per eliminare la bobina {bobina.numero_bobina} ({bobina.id_bobina})\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"py-4\">\n          {/* Informazioni bobina */}\n          <div className=\"bg-slate-50 p-4 rounded-lg mb-4\">\n            <h4 className=\"font-medium mb-2\">Dettagli bobina:</h4>\n            <div className=\"text-sm space-y-1\">\n              <div><strong>ID:</strong> {bobina.id_bobina}</div>\n              <div><strong>Numero:</strong> {bobina.numero_bobina}</div>\n              <div><strong>Utility:</strong> {bobina.utility}</div>\n              <div><strong>Tipologia:</strong> {bobina.tipologia}</div>\n              <div><strong>Sezione:</strong> {bobina.sezione}</div>\n              <div><strong>Stato:</strong> {bobina.stato_bobina}</div>\n              <div><strong>Metri:</strong> {bobina.metri_residui}m / {bobina.metri_totali}m</div>\n              {bobina.ubicazione_bobina && (\n                <div><strong>Ubicazione:</strong> {bobina.ubicazione_bobina}</div>\n              )}\n            </div>\n          </div>\n\n          {/* Avviso */}\n          <Alert variant={warningInfo.type === 'error' ? 'destructive' : 'default'}>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <div>\n              <div className=\"font-medium\">{warningInfo.title}</div>\n              <AlertDescription className=\"mt-1\">\n                {warningInfo.message}\n              </AlertDescription>\n            </div>\n          </Alert>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            variant=\"destructive\" \n            onClick={handleDelete} \n            disabled={loading || !canDelete}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Eliminando...' : 'Elimina Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAjBA;;;;;;;;;;AA2Be,SAAS,oBAAoB,EAC1C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACkB;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,kEAAkE;YAClE,MAAM,eAAe,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAEpD,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAEvE,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC,uBAAuB,CAAC;YAErE,yDAAyD;YACzD,IAAI,SAAS,IAAI,EAAE,gBAAgB;gBACjC,WAAW;YACb;YAEA,UAAU;YACV;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,6CAA6C;IAC7C,MAAM,YAAY,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,KAClC,OAAO,aAAa,KAAK,OAAO,YAAY;IAE7D,0CAA0C;IAC1C,MAAM,iBAAiB;QACrB,IAAI,CAAC,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,GAAG;YACvC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,sBAAsB,EAAE,OAAO,YAAY,CAAC,8BAA8B,EAAE,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY,GAAG;YACtI;QACF;QAEA,IAAI,OAAO,aAAa,KAAK,OAAO,YAAY,EAAE;YAChD,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC,4EAA4E,CAAC;YAChK;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAG7C,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACa,OAAO,aAAa;gCAAC;gCAAG,OAAO,SAAS;gCAAC;;;;;;;;;;;;;8BAI3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAY;gDAAE,OAAO,SAAS;;;;;;;sDAC3C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE,OAAO,aAAa;;;;;;;sDACnD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE,OAAO,SAAS;;;;;;;sDAClD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,YAAY;;;;;;;sDACjD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,aAAa;gDAAC;gDAAK,OAAO,YAAY;gDAAC;;;;;;;wCAC3E,OAAO,iBAAiB,kBACvB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;sCAMjE,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS,YAAY,IAAI,KAAK,UAAU,gBAAgB;;8CAC7D,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAe,YAAY,KAAK;;;;;;sDAC/C,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDACzB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,WAAW,CAAC;;gCAErB,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/VisualizzaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Eye, Package, Calendar, MapPin, Truck, FileText } from 'lucide-react'\nimport { ParcoCavo } from '@/types'\nimport { \n  getReelStateColor, \n  getReelStateDescription, \n  calculateReelUsagePercentage,\n  formatMeters \n} from '@/utils/bobineUtils'\n\ninterface VisualizzaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n}\n\nexport default function VisualizzaBobinaDialog({\n  open,\n  onClose,\n  bobina\n}: VisualizzaBobinaDialogProps) {\n  if (!bobina) return null\n\n  const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)\n  const statoColorClass = getReelStateColor(bobina.stato_bobina)\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Eye className=\"h-5 w-5\" />\n            Dettagli Bobina {bobina.numero_bobina}\n          </DialogTitle>\n          <DialogDescription>\n            Informazioni complete sulla bobina {bobina.id_bobina}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-6 py-4\">\n          {/* Informazioni principali */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"font-semibold text-lg mb-3 flex items-center gap-2\">\n                  <Package className=\"h-5 w-5\" />\n                  Identificazione\n                </h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">ID Bobina:</span>\n                    <span className=\"font-medium\">{bobina.id_bobina}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">Numero:</span>\n                    <span className=\"font-medium\">{bobina.numero_bobina}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">Utility:</span>\n                    <span className=\"font-medium\">{bobina.utility || '-'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">Configurazione:</span>\n                    <span className=\"font-medium\">\n                      {bobina.configurazione === 's' ? 'Standard' : 'Manuale'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"font-semibold text-lg mb-3\">Specifiche Tecniche</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">Tipologia:</span>\n                    <span className=\"font-medium\">{bobina.tipologia || '-'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">N° Conduttori:</span>\n                    <span className=\"font-medium\">{bobina.n_conduttori || '-'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">Sezione:</span>\n                    <span className=\"font-medium\">{bobina.sezione || '-'}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"font-semibold text-lg mb-3\">Stato e Utilizzo</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-slate-600\">Stato:</span>\n                    <Badge className={statoColorClass} title={getReelStateDescription(bobina.stato_bobina)}>\n                      {bobina.stato_bobina}\n                    </Badge>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-slate-600\">Metri Totali:</span>\n                      <span className=\"font-medium\">{formatMeters(bobina.metri_totali)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-slate-600\">Metri Residui:</span>\n                      <span className=\"font-medium\">{formatMeters(bobina.metri_residui)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-slate-600\">Metri Utilizzati:</span>\n                      <span className=\"font-medium\">\n                        {formatMeters(bobina.metri_totali - bobina.metri_residui)}\n                      </span>\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-slate-600\">Utilizzo:</span>\n                      <span className=\"font-medium\">{Math.round(percentualeUtilizzo)}%</span>\n                    </div>\n                    <Progress value={percentualeUtilizzo} className=\"h-3\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Informazioni logistiche */}\n          <div className=\"border-t pt-4\">\n            <h3 className=\"font-semibold text-lg mb-3 flex items-center gap-2\">\n              <MapPin className=\"h-5 w-5\" />\n              Informazioni Logistiche\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <MapPin className=\"h-4 w-4 text-slate-500\" />\n                  <span className=\"text-slate-600\">Ubicazione:</span>\n                  <span className=\"font-medium\">{bobina.ubicazione_bobina || 'Non specificata'}</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Truck className=\"h-4 w-4 text-slate-500\" />\n                  <span className=\"text-slate-600\">Fornitore:</span>\n                  <span className=\"font-medium\">{bobina.fornitore || 'Non specificato'}</span>\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <FileText className=\"h-4 w-4 text-slate-500\" />\n                  <span className=\"text-slate-600\">N° DDT:</span>\n                  <span className=\"font-medium\">{bobina.n_DDT || 'Non specificato'}</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Calendar className=\"h-4 w-4 text-slate-500\" />\n                  <span className=\"text-slate-600\">Data DDT:</span>\n                  <span className=\"font-medium\">\n                    {bobina.data_DDT ? new Date(bobina.data_DDT).toLocaleDateString('it-IT') : 'Non specificata'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Informazioni aggiuntive */}\n          {(bobina.metri_residui < 0 || bobina.stato_bobina === 'Over') && (\n            <div className=\"border-t pt-4\">\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-red-800 mb-2\">⚠️ Attenzione - Bobina Sovra-utilizzata</h4>\n                <p className=\"text-red-700 text-sm\">\n                  Questa bobina ha metri residui negativi ({formatMeters(bobina.metri_residui)}), \n                  indicando che sono stati posati più metri di quelli disponibili. \n                  Verificare l'accuratezza delle misurazioni e considerare una revisione dei dati.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {bobina.stato_bobina === 'Terminata' && (\n            <div className=\"border-t pt-4\">\n              <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-orange-800 mb-2\">📋 Bobina Terminata</h4>\n                <p className=\"text-orange-700 text-sm\">\n                  Questa bobina è completamente esaurita. Non è più disponibile per nuove installazioni.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {bobina.stato_bobina === 'In uso' && (\n            <div className=\"border-t pt-4\">\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-blue-800 mb-2\">🔄 Bobina in Uso</h4>\n                <p className=\"text-blue-700 text-sm\">\n                  Questa bobina è parzialmente utilizzata. Disponibili ancora {formatMeters(bobina.metri_residui)} \n                  per nuove installazioni.\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAbA;;;;;;;AA0Be,SAAS,uBAAuB,EAC7C,IAAI,EACJ,OAAO,EACP,MAAM,EACsB;IAC5B,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,sBAAsB,CAAA,GAAA,2HAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;IAClG,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;IAE7D,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;gCACV,OAAO,aAAa;;;;;;;sCAEvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,OAAO,SAAS;;;;;;;;;;;;;8BAIxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,SAAS;;;;;;;;;;;;sEAEjD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,aAAa;;;;;;;;;;;;sEAErD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,OAAO,IAAI;;;;;;;;;;;;sEAEnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EACb,OAAO,cAAc,KAAK,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sDAMtD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,SAAS,IAAI;;;;;;;;;;;;sEAErD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,YAAY,IAAI;;;;;;;;;;;;sEAExD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAW;gEAAiB,OAAO,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY;0EAClF,OAAO,YAAY;;;;;;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;;;;;;;;;;;;0EAEjE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;0EAElE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,8OAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY,GAAG,OAAO,aAAa;;;;;;;;;;;;;;;;;;kEAI9D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiB;;;;;;kFACjC,8OAAC;wEAAK,WAAU;;4EAAe,KAAK,KAAK,CAAC;4EAAqB;;;;;;;;;;;;;0EAEjE,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,OAAO;gEAAqB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8OAAC;4DAAK,WAAU;sEAAe,OAAO,iBAAiB,IAAI;;;;;;;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8OAAC;4DAAK,WAAU;sEAAe,OAAO,SAAS,IAAI;;;;;;;;;;;;;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8OAAC;4DAAK,WAAU;sEAAe,OAAO,KAAK,IAAI;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8OAAC;4DAAK,WAAU;sEACb,OAAO,QAAQ,GAAG,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQpF,CAAC,OAAO,aAAa,GAAG,KAAK,OAAO,YAAY,KAAK,MAAM,mBAC1D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;;4CAAuB;4CACQ,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;4CAAE;;;;;;;;;;;;;;;;;;wBAQpF,OAAO,YAAY,KAAK,6BACvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;wBAO5C,OAAO,YAAY,KAAK,0BACvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAE,WAAU;;4CAAwB;4CAC0B,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlH", "debugId": null}}, {"offset": {"line": 3622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/parco-cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { parcoCaviApi } from '@/lib/api'\nimport { ParcoCavo } from '@/types'\nimport {\n  REEL_STATES,\n  getReelStateColor,\n  getReelRowColor,\n  determineReelState,\n  calculateReelUsagePercentage,\n  formatMeters,\n  getReelStateDescription\n} from '@/utils/bobineUtils'\nimport CreaBobinaDialog from '@/components/bobine/CreaBobinaDialog'\nimport ModificaBobinaDialog from '@/components/bobine/ModificaBobinaDialog'\nimport EliminaBobinaDialog from '@/components/bobine/EliminaBobinaDialog'\nimport VisualizzaBobinaDialog from '@/components/bobine/VisualizzaBobinaDialog'\nimport { \n  Package, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Eye,\n  Download,\n  Upload,\n  Loader2\n} from 'lucide-react'\n\nexport default function ParcoCaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [bobine, setBobine] = useState<ParcoCavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere } = useAuth()\n\n  // Stati per i dialoghi\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\n  const [showViewDialog, setShowViewDialog] = useState(false)\n  const [selectedBobina, setSelectedBobina] = useState<ParcoCavo | null>(null)\n\n  // Stati per notifiche\n  const [successMessage, setSuccessMessage] = useState('')\n  const [errorMessage, setErrorMessage] = useState('')\n\n  // Carica le bobine dal backend\n  useEffect(() => {\n    loadBobine()\n  }, [])\n\n  const loadBobine = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n      \n      const cantiereId = cantiere?.id_cantiere || user?.id_utente\n      if (!cantiereId) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      const data = await parcoCaviApi.getBobine(cantiereId)\n      setBobine(data)\n    } catch (error: any) {\n      console.error('Errore caricamento bobine:', error)\n      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Ricarica quando cambiano i filtri\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      loadBobine()\n    }, 500)\n\n    return () => clearTimeout(timeoutId)\n  }, [searchTerm, selectedStatus])\n\n  // Gestione notifiche\n  useEffect(() => {\n    if (successMessage) {\n      const timer = setTimeout(() => setSuccessMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [successMessage])\n\n  useEffect(() => {\n    if (errorMessage) {\n      const timer = setTimeout(() => setErrorMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [errorMessage])\n\n  // Funzioni per gestire i dialoghi\n  const handleViewBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowViewDialog(true)\n  }\n\n  const handleEditBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowEditDialog(true)\n  }\n\n  const handleDeleteBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowDeleteDialog(true)\n  }\n\n  const handleCreateSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleCreateError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleEditSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleEditError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleDeleteSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleDeleteError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {\n    // Determina lo stato effettivo della bobina\n    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)\n    const colorClass = getReelStateColor(statoEffettivo)\n\n    return (\n      <Badge\n        className={colorClass}\n        title={getReelStateDescription(statoEffettivo)}\n      >\n        {statoEffettivo}\n      </Badge>\n    )\n  }\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.id_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      // Determina lo stato effettivo della bobina\n      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n\n      switch (selectedStatus) {\n        case 'disponibile':\n          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE\n          break\n        case 'in_uso':\n          matchesStatus = statoEffettivo === REEL_STATES.IN_USO\n          break\n        case 'esaurita':\n          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA\n          break\n        case 'over':\n          matchesStatus = statoEffettivo === REEL_STATES.OVER\n          break\n      }\n    }\n\n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: bobine.length,\n    disponibili: bobine.filter(b => {\n      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)\n      return stato === REEL_STATES.DISPONIBILE\n    }).length,\n    in_uso: bobine.filter(b => {\n      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)\n      return stato === REEL_STATES.IN_USO\n    }).length,\n    esaurite: bobine.filter(b => {\n      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)\n      return stato === REEL_STATES.TERMINATA\n    }).length,\n    over: bobine.filter(b => {\n      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)\n      return stato === REEL_STATES.OVER\n    }).length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n        \n        {/* Action buttons */}\n        <div className=\"flex justify-end gap-2 mb-6\">\n          <Button variant=\"outline\" size=\"sm\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Esporta\n          </Button>\n          <Button variant=\"outline\" size=\"sm\">\n            <Upload className=\"h-4 w-4 mr-2\" />\n            Importa\n          </Button>\n          <Button size=\"sm\" onClick={() => setShowCreateDialog(true)}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Nuova Bobina\n          </Button>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Totali</p>\n                  <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n                </div>\n                <Package className=\"h-8 w-8 text-blue-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Disponibili</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{stats.disponibili}</p>\n                </div>\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">In Uso</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_uso}</p>\n                </div>\n                <Clock className=\"h-8 w-8 text-yellow-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Esaurite</p>\n                  <p className=\"text-2xl font-bold text-red-600\">{stats.esaurite}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-red-500\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Over</p>\n                  <p className=\"text-2xl font-bold text-red-800\">{stats.over}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-red-800\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per ID bobina, numero, tipologia o utility...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'disponibile', 'in_uso', 'esaurita', 'over'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutte' :\n                     status === 'disponibile' ? 'Disponibili' :\n                     status === 'in_uso' ? 'In Uso' :\n                     status === 'esaurita' ? 'Esaurite' : 'Over'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Bobine Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>\n            <CardDescription>\n              Gestione completa delle bobine con stato utilizzo e metrature\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>ID Bobina</TableHead>\n                    <TableHead>Numero</TableHead>\n                    <TableHead>Utility</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Conduttori/Sezione</TableHead>\n                    <TableHead>Metrature</TableHead>\n                    <TableHead>Utilizzo</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Ubicazione</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento bobine...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredBobine.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8 text-slate-500\">\n                        Nessuna bobina trovata\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredBobine.map((bobina) => {\n                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)\n                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n                      const rowColorClass = getReelRowColor(statoEffettivo)\n\n                      return (\n                        <TableRow\n                          key={bobina.id_bobina}\n                          className={`transition-colors ${rowColorClass}`}\n                        >\n                          <TableCell className=\"font-medium\">{bobina.id_bobina}</TableCell>\n                          <TableCell>{bobina.numero_bobina || '-'}</TableCell>\n                          <TableCell>{bobina.utility || '-'}</TableCell>\n                          <TableCell>{bobina.tipologia || '-'}</TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>{bobina.n_conduttori || '-'}</div>\n                              <div className=\"text-slate-500\">{bobina.sezione || '-'}</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>Residui: <span className=\"font-medium\">{formatMeters(bobina.metri_residui)}</span></div>\n                              <div className=\"text-slate-500\">Totali: {formatMeters(bobina.metri_totali)}</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"space-y-1\">\n                              <div className=\"text-sm font-medium\">{Math.round(percentualeUtilizzo)}%</div>\n                              <Progress value={percentualeUtilizzo} className=\"h-2\" />\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}\n                          </TableCell>\n                          <TableCell>\n                            <Badge variant=\"outline\">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-1\">\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleViewBobina(bobina)}\n                                title=\"Visualizza dettagli\"\n                              >\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleEditBobina(bobina)}\n                                title=\"Modifica bobina\"\n                              >\n                                <Edit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleDeleteBobina(bobina)}\n                                title=\"Elimina bobina\"\n                              >\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      )\n                    })\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Notifiche */}\n        {successMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert className=\"bg-green-50 border-green-200\">\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\n              <AlertDescription className=\"text-green-800\">\n                {successMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                {errorMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n      </div>\n\n      {/* Dialoghi */}\n      <CreaBobinaDialog\n        open={showCreateDialog}\n        onClose={() => setShowCreateDialog(false)}\n        onSuccess={handleCreateSuccess}\n        onError={handleCreateError}\n      />\n\n      <ModificaBobinaDialog\n        open={showEditDialog}\n        onClose={() => setShowEditDialog(false)}\n        bobina={selectedBobina}\n        onSuccess={handleEditSuccess}\n        onError={handleEditError}\n      />\n\n      <EliminaBobinaDialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n        bobina={selectedBobina}\n        onSuccess={handleDeleteSuccess}\n        onError={handleDeleteError}\n      />\n\n      <VisualizzaBobinaDialog\n        open={showViewDialog}\n        onClose={() => setShowViewDialog(false)}\n        bobina={selectedBobina}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA1BA;;;;;;;;;;;;;;;;;;AAyCe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEjC,uBAAuB;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvE,sBAAsB;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,aAAa,UAAU,eAAe,MAAM;YAClD,IAAI,CAAC,YAAY;gBACf,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAC1C,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAY;KAAe;IAE/B,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,MAAM,QAAQ,WAAW,IAAM,kBAAkB,KAAK;YACtD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,MAAM,QAAQ,WAAW,IAAM,gBAAgB,KAAK;YACpD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAa;IAEjB,kCAAkC;IAClC,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC,OAAe,eAAuB;QAC5D,4CAA4C;QAC5C,MAAM,iBAAiB,SAAS,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;QAClE,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,oBAAiB,AAAD,EAAE;QAErC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YACJ,WAAW;YACX,OAAO,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;sBAE9B;;;;;;IAGP;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACnE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,OAAO,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAElF,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,4CAA4C;YAC5C,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;YAE1G,OAAQ;gBACN,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,WAAW;oBAC1D;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,MAAM;oBACrD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,SAAS;oBACxD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI;oBACnD;YACJ;QACF;QAEA,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,OAAO,MAAM;QACrB,aAAa,OAAO,MAAM,CAAC,CAAA;YACzB,MAAM,QAAQ,EAAE,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,EAAE,aAAa,EAAE,EAAE,YAAY;YAClF,OAAO,UAAU,2HAAA,CAAA,cAAW,CAAC,WAAW;QAC1C,GAAG,MAAM;QACT,QAAQ,OAAO,MAAM,CAAC,CAAA;YACpB,MAAM,QAAQ,EAAE,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,EAAE,aAAa,EAAE,EAAE,YAAY;YAClF,OAAO,UAAU,2HAAA,CAAA,cAAW,CAAC,MAAM;QACrC,GAAG,MAAM;QACT,UAAU,OAAO,MAAM,CAAC,CAAA;YACtB,MAAM,QAAQ,EAAE,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,EAAE,aAAa,EAAE,EAAE,YAAY;YAClF,OAAO,UAAU,2HAAA,CAAA,cAAW,CAAC,SAAS;QACxC,GAAG,MAAM;QACT,MAAM,OAAO,MAAM,CAAC,CAAA;YAClB,MAAM,QAAQ,EAAE,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,EAAE,aAAa,EAAE,EAAE,YAAY;YAClF,OAAO,UAAU,2HAAA,CAAA,cAAW,CAAC,IAAI;QACnC,GAAG,MAAM;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS,IAAM,oBAAoB;;kDACnD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAqC,MAAM,MAAM;;;;;;;;;;;;0DAEhE,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKzB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAqC,MAAM,WAAW;;;;;;;;;;;;0DAErE,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAsC,MAAM,MAAM;;;;;;;;;;;;0DAEjE,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKvB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAmC,MAAM,QAAQ;;;;;;;;;;;;0DAEhE,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAmC,MAAM,IAAI;;;;;;;;;;;;0DAE5D,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAO;gDAAe;gDAAU;gDAAY;6CAAO,CAAC,GAAG,CAAC,CAAC,uBACzD,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,mBAAmB,SAAS,YAAY;oDACjD,MAAK;oDACL,SAAS,IAAM,kBAAkB;8DAEhC,WAAW,QAAQ,UACnB,WAAW,gBAAgB,gBAC3B,WAAW,WAAW,WACtB,WAAW,aAAa,aAAa;mDARjC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBjB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;4CAAgB,eAAe,MAAM;4CAAC;;;;;;;kDACjD,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0DACJ,8OAAC,iIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sEACP,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;;;;;;0DAGf,8OAAC,iIAAA,CAAA,YAAS;0DACP,0BACC,8OAAC,iIAAA,CAAA,WAAQ;8DACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAI,WAAU;kEAChC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAyB;;;;;;;;;;;;;;;;2DAKhD,sBACF,8OAAC,iIAAA,CAAA,WAAQ;8DACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAI,WAAU;kEAChC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB;;;;;;;;;;;;;;;;2DAIL,eAAe,MAAM,KAAK,kBAC5B,8OAAC,iIAAA,CAAA,WAAQ;8DACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAI,WAAU;kEAAkC;;;;;;;;;;2DAKtE,eAAe,GAAG,CAAC,CAAC;oDAClB,MAAM,sBAAsB,CAAA,GAAA,2HAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;oDAClG,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;oDAC1G,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;oDAEtC,qBACE,8OAAC,iIAAA,CAAA,WAAQ;wDAEP,WAAW,CAAC,kBAAkB,EAAE,eAAe;;0EAE/C,8OAAC,iIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAe,OAAO,SAAS;;;;;;0EACpD,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,aAAa,IAAI;;;;;;0EACpC,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,OAAO,IAAI;;;;;;0EAC9B,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,SAAS,IAAI;;;;;;0EAChC,8OAAC,iIAAA,CAAA,YAAS;0EACR,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK,OAAO,YAAY,IAAI;;;;;;sFAC7B,8OAAC;4EAAI,WAAU;sFAAkB,OAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;0EAGvD,8OAAC,iIAAA,CAAA,YAAS;0EACR,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAI;8FAAS,8OAAC;oFAAK,WAAU;8FAAe,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;sFAC9E,8OAAC;4EAAI,WAAU;;gFAAiB;gFAAS,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;;;;;;;;;;;;;;;;;;0EAG7E,8OAAC,iIAAA,CAAA,YAAS;0EACR,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFAAuB,KAAK,KAAK,CAAC;gFAAqB;;;;;;;sFACtE,8OAAC,oIAAA,CAAA,WAAQ;4EAAC,OAAO;4EAAqB,WAAU;;;;;;;;;;;;;;;;;0EAGpD,8OAAC,iIAAA,CAAA,YAAS;0EACP,eAAe,OAAO,YAAY,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;;;;;;0EAEhF,8OAAC,iIAAA,CAAA,YAAS;0EACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,OAAO,iBAAiB,IAAI;;;;;;;;;;;0EAExD,8OAAC,iIAAA,CAAA,YAAS;0EACR,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,iBAAiB;4EAChC,OAAM;sFAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,iBAAiB;4EAChC,OAAM;sFAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,mBAAmB;4EAClC,OAAM;sFAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uDAvDnB,OAAO,SAAS;;;;;gDA6D3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASX,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;;;;;;oBAMR,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CACd;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC,gJAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,oJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,mJAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,QAAQ;gBACR,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,sJAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}]}