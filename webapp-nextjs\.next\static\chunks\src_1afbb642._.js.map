{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/bobineUtils.ts"], "sourcesContent": ["/**\n * Utility per la gestione degli stati delle bobine\n * Implementa le stesse regole della webapp originale\n */\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n} as const\n\nexport type ReelState = typeof REEL_STATES[keyof typeof REEL_STATES]\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Stato della bobina\n */\nexport const determineReelState = (metriResidui: number, metriTotali: number): ReelState => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER\n  }\n\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA\n  }\n\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO\n  }\n\n  return REEL_STATES.DISPONIBILE\n}\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param statoBobina - Stato della bobina\n * @returns True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = (statoBobina: string): boolean => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. È in stato IN_USO\n  // 3. Non è in stato TERMINATA o OVER\n  return statoBobina === REEL_STATES.DISPONIBILE || \n         statoBobina === REEL_STATES.IN_USO\n}\n\n/**\n * Ottiene il colore associato a uno stato della bobina per i badge\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore del badge\n */\nexport const getReelStateColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'bg-green-100 text-green-800'\n    case REEL_STATES.IN_USO:\n      return 'bg-yellow-100 text-yellow-800'\n    case REEL_STATES.TERMINATA:\n      return 'bg-red-100 text-red-800'\n    case REEL_STATES.OVER:\n      return 'bg-red-500 text-white'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\n/**\n * Ottiene il colore di sfondo per le righe della tabella in base allo stato\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore di sfondo\n */\nexport const getReelRowColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'hover:bg-green-50'\n    case REEL_STATES.IN_USO:\n      return 'hover:bg-yellow-50'\n    case REEL_STATES.TERMINATA:\n      return 'hover:bg-red-50'\n    case REEL_STATES.OVER:\n      return 'hover:bg-red-100'\n    default:\n      return 'hover:bg-gray-50'\n  }\n}\n\n/**\n * Verifica se una bobina è utilizzabile per nuove installazioni\n * @param statoBobina - Stato della bobina\n * @param metriResidui - Metri residui\n * @returns True se la bobina è utilizzabile\n */\nexport const isReelUsable = (statoBobina: string, metriResidui: number): boolean => {\n  return (statoBobina === REEL_STATES.DISPONIBILE || statoBobina === REEL_STATES.IN_USO) && \n         metriResidui > 0\n}\n\n/**\n * Calcola la percentuale di utilizzo di una bobina\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Percentuale di utilizzo (0-100)\n */\nexport const calculateReelUsagePercentage = (metriResidui: number, metriTotali: number): number => {\n  if (metriTotali <= 0) return 0\n  const utilizzo = ((metriTotali - metriResidui) / metriTotali) * 100\n  return Math.max(0, Math.min(100, utilizzo))\n}\n\n/**\n * Formatta i metri per la visualizzazione\n * @param metri - Metri da formattare\n * @returns Stringa formattata con unità\n */\nexport const formatMeters = (metri: number): string => {\n  return `${metri.toFixed(1)}m`\n}\n\n/**\n * Ottiene una descrizione testuale dello stato della bobina\n * @param stato - Stato della bobina\n * @returns Descrizione dello stato\n */\nexport const getReelStateDescription = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'Bobina disponibile per nuove installazioni'\n    case REEL_STATES.IN_USO:\n      return 'Bobina parzialmente utilizzata'\n    case REEL_STATES.TERMINATA:\n      return 'Bobina completamente esaurita'\n    case REEL_STATES.OVER:\n      return 'Bobina sovra-utilizzata (metri negativi)'\n    default:\n      return 'Stato non definito'\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,qBAAqB;;;;;;;;;;;;AACd,MAAM,cAAc;IACzB,aAAa;IACb,QAAQ;IACR,WAAW;IACX,MAAM;AACR;AAUO,MAAM,qBAAqB,CAAC,cAAsB;IACvD,IAAI,eAAe,GAAG;QACpB,OAAO,YAAY,IAAI;IACzB;IAEA,IAAI,iBAAiB,GAAG;QACtB,OAAO,YAAY,SAAS;IAC9B;IAEA,IAAI,eAAe,aAAa;QAC9B,OAAO,YAAY,MAAM;IAC3B;IAEA,OAAO,YAAY,WAAW;AAChC;AAOO,MAAM,gBAAgB,CAAC;IAC5B,uCAAuC;IACvC,4BAA4B;IAC5B,uBAAuB;IACvB,qCAAqC;IACrC,OAAO,gBAAgB,YAAY,WAAW,IACvC,gBAAgB,YAAY,MAAM;AAC3C;AAOO,MAAM,oBAAoB,CAAC;IAChC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAOO,MAAM,kBAAkB,CAAC;IAC9B,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAQO,MAAM,eAAe,CAAC,aAAqB;IAChD,OAAO,CAAC,gBAAgB,YAAY,WAAW,IAAI,gBAAgB,YAAY,MAAM,KAC9E,eAAe;AACxB;AAQO,MAAM,+BAA+B,CAAC,cAAsB;IACjE,IAAI,eAAe,GAAG,OAAO;IAC7B,MAAM,WAAW,AAAC,CAAC,cAAc,YAAY,IAAI,cAAe;IAChE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;AACnC;AAOO,MAAM,eAAe,CAAC;IAC3B,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAOO,MAAM,0BAA0B,CAAC;IACtC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/CreaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Plus } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\n\n\ninterface CreaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\nconst initialFormData: FormData = {\n  numero_bobina: '',\n  utility: '',\n  tipologia: '',\n  n_conduttori: '0',\n  sezione: '',\n  metri_totali: '',\n  ubicazione_bobina: 'TBD',\n  fornitore: 'TBD',\n  n_DDT: 'TBD',\n  data_DDT: '',\n  configurazione: 's'\n}\n\nexport default function CreaBobinaDialog({\n  open,\n  onClose,\n  cantiereId,\n  onSuccess,\n  onError\n}: CreaBobinaDialogProps) {\n  const [formData, setFormData] = useState<FormData>(initialFormData)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [nextBobinaNumber, setNextBobinaNumber] = useState('1')\n  const [loadingConfig, setLoadingConfig] = useState(false)\n  const [isFirstInsertion, setIsFirstInsertion] = useState(true)\n  const [configurazioneFixed, setConfigurazioneFixed] = useState('')\n  const [showConfigSelection, setShowConfigSelection] = useState(false)\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId && cantiereId > 0) {\n      setFormData(initialFormData)\n      setError('')\n      checkFirstInsertion()\n    }\n  }, [open, cantiereId])\n\n  // Verifica se è il primo inserimento e carica la configurazione\n  const checkFirstInsertion = async () => {\n    if (!cantiereId || cantiereId <= 0) return\n\n    try {\n      setLoadingConfig(true)\n      const response = await parcoCaviApi.isFirstBobinaInsertion(cantiereId)\n\n      setIsFirstInsertion(response.is_first_insertion)\n\n      if (response.is_first_insertion) {\n        // Primo inserimento: mostra selezione configurazione\n        setShowConfigSelection(true)\n        setConfigurazioneFixed('')\n      } else {\n        // Non è il primo inserimento: usa configurazione esistente\n        setConfigurazioneFixed(response.configurazione)\n        setFormData(prev => ({\n          ...prev,\n          configurazione: response.configurazione\n        }))\n        setShowConfigSelection(false)\n\n        // Carica il prossimo numero se configurazione è standard\n        if (response.configurazione === 's') {\n          await loadNextBobinaNumber()\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel controllo primo inserimento:', error)\n      // In caso di errore, assume primo inserimento\n      setIsFirstInsertion(true)\n      setShowConfigSelection(true)\n    } finally {\n      setLoadingConfig(false)\n    }\n  }\n\n  // Funzione per caricare il prossimo numero bobina (numerazione globale)\n  const loadNextBobinaNumber = async () => {\n    if (!cantiereId || cantiereId <= 0) return\n\n    try {\n      // Per la numerazione globale, recupera TUTTE le bobine di TUTTI i cantieri\n      // e trova il numero massimo globale\n      const bobine = await parcoCaviApi.getBobine(cantiereId)\n\n      if (bobine && bobine.length > 0) {\n        // Filtra solo le bobine con numero_bobina numerico\n        const numericBobine = bobine.filter(b =>\n          b.numero_bobina && /^\\d+$/.test(b.numero_bobina)\n        )\n\n        if (numericBobine.length > 0) {\n          // Trova il numero massimo tra le bobine esistenti\n          const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)))\n          const nextNumber = String(maxNumber + 1)\n          setNextBobinaNumber(nextNumber)\n\n          // Se la configurazione è standard, imposta automaticamente il numero\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: nextNumber\n          }))\n        } else {\n          setNextBobinaNumber('1')\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: '1'\n          }))\n        }\n      } else {\n        setNextBobinaNumber('1')\n        setFormData(prev => ({\n          ...prev,\n          numero_bobina: '1'\n        }))\n      }\n    } catch (error) {\n      console.error('Errore nel recupero del prossimo numero bobina:', error)\n      setNextBobinaNumber('1')\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: '1'\n      }))\n    }\n  }\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError('')\n  }\n\n  // Gestisce la selezione della configurazione (solo al primo inserimento)\n  const handleConfigSelection = async (config: string) => {\n    setConfigurazioneFixed(config)\n    setFormData(prev => ({\n      ...prev,\n      configurazione: config\n    }))\n    setShowConfigSelection(false)\n\n    // Se configurazione standard, carica il prossimo numero\n    if (config === 's') {\n      await loadNextBobinaNumber()\n    } else {\n      // Configurazione manuale: svuota il campo\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: ''\n      }))\n    }\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.numero_bobina.trim()) {\n      return 'Il numero bobina è obbligatorio'\n    }\n\n    // Validazione per configurazione manuale\n    if (formData.configurazione === 'm') {\n      const numeroInput = formData.numero_bobina.trim()\n\n      // Verifica caratteri non consentiti\n      if (/[\\s\\\\/:*?\"<>|]/.test(numeroInput)) {\n        return 'Il numero bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \" < > |'\n      }\n    }\n\n    if (!formData.utility.trim()) {\n      return 'La utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      return 'La tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      return 'La formazione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      return 'I metri totali sono obbligatori'\n    }\n\n    const metri = parseFloat(formData.metri_totali)\n    if (isNaN(metri) || metri <= 0) {\n      return 'I metri totali devono essere un numero positivo'\n    }\n\n    return null\n  }\n\n  const handleSave = async () => {\n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiereId || cantiereId <= 0) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Prepara i dati per l'API\n      const bobinaData = {\n        numero_bobina: formData.numero_bobina.trim(),\n        utility: formData.utility.trim().toUpperCase(),\n        tipologia: formData.tipologia.trim().toUpperCase(),\n        n_conduttori: '0', // Campo disponibile, sempre impostato a '0'\n        sezione: formData.sezione.trim(),\n        metri_totali: parseFloat(formData.metri_totali),\n        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',\n        fornitore: formData.fornitore.trim() || 'TBD',\n        n_DDT: formData.n_DDT.trim() || 'TBD',\n        data_DDT: formData.data_DDT || null,\n        configurazione: formData.configurazione\n      }\n\n      await parcoCaviApi.createBobina(cantiereId, bobinaData)\n\n      onSuccess(`Bobina ${formData.numero_bobina} creata con successo`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella creazione bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFormData(initialFormData)\n      setError('')\n      onClose()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Plus className=\"h-5 w-5\" />\n            Crea Nuova Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Inserisci i dati per creare una nuova bobina nel parco cavi\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n          {/* Caricamento configurazione */}\n          {loadingConfig && (\n            <div className=\"flex items-center justify-center py-4\">\n              <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n              <span>Verifica configurazione...</span>\n            </div>\n          )}\n\n          {/* Selezione configurazione (solo primo inserimento) */}\n          {showConfigSelection && !loadingConfig && (\n            <div className=\"border rounded-lg p-4 bg-blue-50\">\n              <h4 className=\"font-medium mb-3\">Seleziona configurazione per questo cantiere</h4>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Questa scelta determinerà come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere.\n              </p>\n              <div className=\"grid grid-cols-1 gap-3\">\n                <Button\n                  variant=\"outline\"\n                  className=\"justify-start h-auto p-4\"\n                  onClick={() => handleConfigSelection('s')}\n                >\n                  <div className=\"text-left\">\n                    <div className=\"font-medium\">Standard (s) - Numerazione automatica</div>\n                    <div className=\"text-sm text-gray-600\">I numeri bobina vengono generati automaticamente: 1, 2, 3...</div>\n                  </div>\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  className=\"justify-start h-auto p-4\"\n                  onClick={() => handleConfigSelection('m')}\n                >\n                  <div className=\"text-left\">\n                    <div className=\"font-medium\">Manuale (m) - Inserimento manuale</div>\n                    <div className=\"text-sm text-gray-600\">Puoi inserire numeri personalizzati: A123, TEST01, ecc.</div>\n                  </div>\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {/* Configurazione fissa (inserimenti successivi) */}\n          {!showConfigSelection && !loadingConfig && configurazioneFixed && (\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label className=\"text-right\">\n                Configurazione\n              </Label>\n              <div className=\"col-span-3\">\n                <div className=\"px-3 py-2 bg-gray-50 border rounded-md text-sm\">\n                  <strong>\n                    {configurazioneFixed === 's' ? 'Standard (s) - Numerazione automatica' : 'Manuale (m) - Inserimento manuale'}\n                  </strong>\n                </div>\n                <div className=\"text-xs text-gray-600 mt-1\">\n                  Configurazione fissa per questo cantiere\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Numero Bobina */}\n          {!showConfigSelection && !loadingConfig && (\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"numero_bobina\" className=\"text-right\">\n                Bobina *\n              </Label>\n              <div className=\"col-span-3 space-y-2\">\n                <Input\n                  id=\"numero_bobina\"\n                  value={formData.numero_bobina}\n                  onChange={(e) => handleInputChange('numero_bobina', e.target.value)}\n                  placeholder={formData.configurazione === 's' ? 'Generato automaticamente' : 'Es: A123, TEST01'}\n                  disabled={loading || formData.configurazione === 's'}\n                  className={formData.configurazione === 's' ? 'bg-gray-50' : ''}\n                />\n                <div className=\"text-sm text-gray-600\">\n                  {formData.configurazione === 's' ? (\n                    <span>\n                      Numero generato automaticamente: <strong>{formData.numero_bobina}</strong>\n                    </span>\n                  ) : (\n                    <span>\n                      Inserisci il numero della bobina (es: 1, A123, TEST01)\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Utility */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"utility\" className=\"text-right\">\n              Utility *\n            </Label>\n            <Input\n              id=\"utility\"\n              value={formData.utility}\n              onChange={(e) => handleInputChange('utility', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: ENEL, TIM, OPEN FIBER\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Tipologia */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"tipologia\" className=\"text-right\">\n              Tipologia *\n            </Label>\n            <Input\n              id=\"tipologia\"\n              value={formData.tipologia}\n              onChange={(e) => handleInputChange('tipologia', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: FO, RAME\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Formazione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"sezione\" className=\"text-right\">\n              Formazione *\n            </Label>\n            <Input\n              id=\"sezione\"\n              value={formData.sezione}\n              onChange={(e) => handleInputChange('sezione', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: 9/125, 50/125, 1.5\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Metri Totali */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"metri_totali\" className=\"text-right\">\n              Metri Totali *\n            </Label>\n            <Input\n              id=\"metri_totali\"\n              type=\"number\"\n              step=\"0.1\"\n              min=\"0\"\n              value={formData.metri_totali}\n              onChange={(e) => handleInputChange('metri_totali', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: 1000\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Ubicazione */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"ubicazione_bobina\" className=\"text-right\">\n              Ubicazione\n            </Label>\n            <Input\n              id=\"ubicazione_bobina\"\n              value={formData.ubicazione_bobina}\n              onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: Magazzino A, Cantiere\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Fornitore */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"fornitore\" className=\"text-right\">\n              Fornitore\n            </Label>\n            <Input\n              id=\"fornitore\"\n              value={formData.fornitore}\n              onChange={(e) => handleInputChange('fornitore', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: Prysmian, Nexans\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Numero DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"n_DDT\" className=\"text-right\">\n              N° DDT\n            </Label>\n            <Input\n              id=\"n_DDT\"\n              value={formData.n_DDT}\n              onChange={(e) => handleInputChange('n_DDT', e.target.value)}\n              className=\"col-span-3\"\n              placeholder=\"Es: DDT001\"\n              disabled={loading}\n            />\n          </div>\n\n          {/* Data DDT */}\n          <div className=\"grid grid-cols-4 items-center gap-4\">\n            <Label htmlFor=\"data_DDT\" className=\"text-right\">\n              Data DDT\n            </Label>\n            <Input\n              id=\"data_DDT\"\n              type=\"date\"\n              value={formData.data_DDT}\n              onChange={(e) => handleInputChange('data_DDT', e.target.value)}\n              className=\"col-span-3\"\n              disabled={loading}\n            />\n          </div>\n\n\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading || showConfigSelection}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={loading || showConfigSelection || loadingConfig}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Creando...' : 'Crea Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AACA;;;AAvBA;;;;;;;;;AAgDA,MAAM,kBAA4B;IAChC,eAAe;IACf,SAAS;IACT,WAAW;IACX,cAAc;IACd,SAAS;IACT,cAAc;IACd,mBAAmB;IACnB,WAAW;IACX,OAAO;IACP,UAAU;IACV,gBAAgB;AAClB;AAEe,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,UAAU,EACV,SAAS,EACT,OAAO,EACe;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,cAAc,aAAa,GAAG;gBACxC,YAAY;gBACZ,SAAS;gBACT;YACF;QACF;qCAAG;QAAC;QAAM;KAAW;IAErB,gEAAgE;IAChE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc,cAAc,GAAG;QAEpC,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC;YAE3D,oBAAoB,SAAS,kBAAkB;YAE/C,IAAI,SAAS,kBAAkB,EAAE;gBAC/B,qDAAqD;gBACrD,uBAAuB;gBACvB,uBAAuB;YACzB,OAAO;gBACL,2DAA2D;gBAC3D,uBAAuB,SAAS,cAAc;gBAC9C,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,gBAAgB,SAAS,cAAc;oBACzC,CAAC;gBACD,uBAAuB;gBAEvB,yDAAyD;gBACzD,IAAI,SAAS,cAAc,KAAK,KAAK;oBACnC,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,8CAA8C;YAC9C,oBAAoB;YACpB,uBAAuB;QACzB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,wEAAwE;IACxE,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc,cAAc,GAAG;QAEpC,IAAI;YACF,2EAA2E;YAC3E,oCAAoC;YACpC,MAAM,SAAS,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAE5C,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;gBAC/B,mDAAmD;gBACnD,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAClC,EAAE,aAAa,IAAI,QAAQ,IAAI,CAAC,EAAE,aAAa;gBAGjD,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,kDAAkD;oBAClD,MAAM,YAAY,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,aAAa,EAAE;oBAC/E,MAAM,aAAa,OAAO,YAAY;oBACtC,oBAAoB;oBAEpB,qEAAqE;oBACrE,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH,OAAO;oBACL,oBAAoB;oBACpB,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH;YACF,OAAO;gBACL,oBAAoB;gBACpB,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,eAAe;oBACjB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,oBAAoB;YACpB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,yEAAyE;IACzE,MAAM,wBAAwB,OAAO;QACnC,uBAAuB;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,gBAAgB;YAClB,CAAC;QACD,uBAAuB;QAEvB,wDAAwD;QACxD,IAAI,WAAW,KAAK;YAClB,MAAM;QACR,OAAO;YACL,0CAA0C;YAC1C,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,OAAO;QACT;QAEA,yCAAyC;QACzC,IAAI,SAAS,cAAc,KAAK,KAAK;YACnC,MAAM,cAAc,SAAS,aAAa,CAAC,IAAI;YAE/C,oCAAoC;YACpC,IAAI,iBAAiB,IAAI,CAAC,cAAc;gBACtC,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAC9C,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,aAAa;gBACjB,eAAe,SAAS,aAAa,CAAC,IAAI;gBAC1C,SAAS,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW;gBAC5C,WAAW,SAAS,SAAS,CAAC,IAAI,GAAG,WAAW;gBAChD,cAAc;gBACd,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,cAAc,WAAW,SAAS,YAAY;gBAC9C,mBAAmB,SAAS,iBAAiB,CAAC,IAAI,MAAM;gBACxD,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,gBAAgB,SAAS,cAAc;YACzC;YAEA,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,YAAY;YAE5C,UAAU,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,oBAAoB,CAAC;YAChE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,YAAY;YACZ,SAAS;YACT;QACF;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAK;;;;;;;;;;;;wBAKT,uBAAuB,CAAC,+BACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAc;;;;;;kEAC7B,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAG3C,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAc;;;;;;kEAC7B,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQhD,CAAC,uBAAuB,CAAC,iBAAiB,qCACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAa;;;;;;8CAG9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DACE,wBAAwB,MAAM,0CAA0C;;;;;;;;;;;sDAG7E,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;wBAQjD,CAAC,uBAAuB,CAAC,+BACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAgB,WAAU;8CAAa;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,aAAa;4CAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAClE,aAAa,SAAS,cAAc,KAAK,MAAM,6BAA6B;4CAC5E,UAAU,WAAW,SAAS,cAAc,KAAK;4CACjD,WAAW,SAAS,cAAc,KAAK,MAAM,eAAe;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;sDACZ,SAAS,cAAc,KAAK,oBAC3B,6LAAC;;oDAAK;kEAC6B,6LAAC;kEAAQ,SAAS,aAAa;;;;;;;;;;;qEAGlE,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;sCAUhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAa;;;;;;8CAGhD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAa;;;;;;8CAGrD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAoB,WAAU;8CAAa;;;;;;8CAG1D,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,iBAAiB;oCACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACtE,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAa;;;;;;8CAGlD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC9D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAQ,WAAU;8CAAa;;;;;;8CAG9C,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAa;;;;;;8CAGjD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;oCACV,UAAU;;;;;;;;;;;;wBAOb,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU,WAAW;sCAAqB;;;;;;sCAG1F,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GAjewB;KAAA", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Edit } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\n\nimport { ParcoCavo } from '@/types'\nimport { canModifyReel } from '@/utils/bobineUtils'\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: string\n  metri_residui: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\nexport default function ModificaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const [formData, setFormData] = useState<FormData>({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Popola il form quando si apre il dialog con una bobina\n  useEffect(() => {\n    if (open && bobina) {\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali?.toString() || '',\n        metri_residui: bobina.metri_residui?.toString() || '',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || 's'\n      })\n      setError('')\n    }\n  }, [open, bobina])\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError('')\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.utility.trim()) {\n      return 'La utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      return 'La tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      return 'La formazione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      return 'I metri totali sono obbligatori'\n    }\n    if (!formData.metri_residui.trim()) {\n      return 'I metri residui sono obbligatori'\n    }\n    \n    const metriTotali = parseFloat(formData.metri_totali)\n    const metriResidui = parseFloat(formData.metri_residui)\n    \n    if (isNaN(metriTotali) || metriTotali <= 0) {\n      return 'I metri totali devono essere un numero positivo'\n    }\n    \n    if (isNaN(metriResidui)) {\n      return 'I metri residui devono essere un numero valido'\n    }\n\n    if (metriResidui > metriTotali) {\n      return 'I metri residui non possono essere maggiori dei metri totali'\n    }\n\n    return null\n  }\n\n  const handleSave = async () => {\n    if (!bobina) return\n\n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiereId || cantiereId <= 0) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Verifica se la bobina può essere modificata completamente\n      const canModifyFully = canModifyReel(bobina.stato_bobina)\n\n      // Prepara i dati per l'API\n      const updateData: any = {\n        // Campi sempre modificabili\n        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',\n        fornitore: formData.fornitore.trim() || 'TBD',\n        n_DDT: formData.n_DDT.trim() || 'TBD',\n        data_DDT: formData.data_DDT || null,\n      }\n\n      // Campi modificabili solo se bobina è disponibile\n      if (canModifyFully) {\n        updateData.utility = formData.utility.trim().toUpperCase()\n        updateData.tipologia = formData.tipologia.trim().toUpperCase()\n        updateData.sezione = formData.sezione.trim()\n        updateData.metri_totali = parseFloat(formData.metri_totali)\n        updateData.n_conduttori = '0' // Campo disponibile, sempre impostato a '0'\n        updateData.configurazione = formData.configurazione\n      }\n\n      // NOTA: metri_residui è un campo di sistema e non viene mai inviato\n\n      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})\n      const bobinaNumero = bobina.id_bobina.split('_B')[1]\n      \n      await parcoCaviApi.updateBobina(cantiereId, bobinaNumero, updateData)\n\n      onSuccess(`Bobina ${formData.numero_bobina} modificata con successo`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella modifica bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  // Verifica se la bobina può essere modificata\n  const canModify = canModifyReel(bobina.stato_bobina)\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"w-[1000px] max-w-[95vw] max-h-[95vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Edit className=\"h-5 w-5\" />\n            Modifica Bobina {bobina.numero_bobina}\n          </DialogTitle>\n          <DialogDescription>\n            {!canModify && (\n              <span className=\"block text-red-600 mt-1\">\n                ⚠️ Attenzione: questa bobina è in stato {bobina.stato_bobina} e potrebbe avere limitazioni\n              </span>\n            )}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n\n\n          {/* Layout a due colonne per i campi */}\n          <div className=\"grid grid-cols-2 gap-6\">\n            {/* Colonna sinistra */}\n            <div className=\"space-y-4\">\n              {/* Utility */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"utility\" className=\"text-right\">\n                  Utility *\n                </Label>\n                <Input\n                  id=\"utility\"\n                  value={formData.utility}\n                  onChange={(e) => handleInputChange('utility', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading || !canModify}\n                  title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}\n                />\n              </div>\n\n              {/* Tipologia */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"tipologia\" className=\"text-right\">\n                  Tipologia *\n                </Label>\n                <Input\n                  id=\"tipologia\"\n                  value={formData.tipologia}\n                  onChange={(e) => handleInputChange('tipologia', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading || !canModify}\n                  title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}\n                />\n              </div>\n\n              {/* Formazione */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"sezione\" className=\"text-right\">\n                  Formazione *\n                </Label>\n                <Input\n                  id=\"sezione\"\n                  value={formData.sezione}\n                  onChange={(e) => handleInputChange('sezione', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading || !canModify}\n                  title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}\n                />\n              </div>\n\n              {/* Metri Totali */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"metri_totali\" className=\"text-right\">\n                  Metri Totali *\n                </Label>\n                <Input\n                  id=\"metri_totali\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  value={formData.metri_totali}\n                  onChange={(e) => handleInputChange('metri_totali', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading || !canModify}\n                  title={!canModify ? 'Modificabile solo se bobina è disponibile' : ''}\n                />\n              </div>\n\n              {/* Data DDT */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"data_DDT\" className=\"text-right\">\n                  Data DDT\n                </Label>\n                <Input\n                  id=\"data_DDT\"\n                  type=\"date\"\n                  value={formData.data_DDT}\n                  onChange={(e) => handleInputChange('data_DDT', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading}\n                />\n              </div>\n            </div>\n\n            {/* Colonna destra */}\n            <div className=\"space-y-4\">\n              {/* Metri Residui */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label className=\"text-right\">\n                  Metri Residui\n                </Label>\n                <div className=\"col-span-3\">\n                  <div className=\"px-3 py-2 bg-gray-50 border rounded-md text-sm\">\n                    <strong>{formData.metri_residui} m</strong>\n                  </div>\n                </div>\n              </div>\n\n              {/* Ubicazione */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"ubicazione_bobina\" className=\"text-right\">\n                  Ubicazione\n                </Label>\n                <Input\n                  id=\"ubicazione_bobina\"\n                  value={formData.ubicazione_bobina}\n                  onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Fornitore */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"fornitore\" className=\"text-right\">\n                  Fornitore\n                </Label>\n                <Input\n                  id=\"fornitore\"\n                  value={formData.fornitore}\n                  onChange={(e) => handleInputChange('fornitore', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Numero DDT */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"n_DDT\" className=\"text-right\">\n                  N° DDT\n                </Label>\n                <Input\n                  id=\"n_DDT\"\n                  value={formData.n_DDT}\n                  onChange={(e) => handleInputChange('n_DDT', e.target.value)}\n                  className=\"col-span-3\"\n                  disabled={loading}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSave} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva Modifiche'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AACA;AAGA;;;AA1BA;;;;;;;;;;AAoDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,eAAe;QACf,SAAS;QACT,WAAW;QACX,cAAc;QACd,SAAS;QACT,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,WAAW;QACX,OAAO;QACP,UAAU;QACV,gBAAgB;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,QAAQ;gBAClB,YAAY;oBACV,eAAe,OAAO,aAAa,IAAI;oBACvC,SAAS,OAAO,OAAO,IAAI;oBAC3B,WAAW,OAAO,SAAS,IAAI;oBAC/B,cAAc,OAAO,YAAY,IAAI;oBACrC,SAAS,OAAO,OAAO,IAAI;oBAC3B,cAAc,OAAO,YAAY,EAAE,cAAc;oBACjD,eAAe,OAAO,aAAa,EAAE,cAAc;oBACnD,mBAAmB,OAAO,iBAAiB,IAAI;oBAC/C,WAAW,OAAO,SAAS,IAAI;oBAC/B,OAAO,OAAO,KAAK,IAAI;oBACvB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,gBAAgB,OAAO,cAAc,IAAI;gBAC3C;gBACA,SAAS;YACX;QACF;yCAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO;QACT;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,OAAO;QACT;QAEA,MAAM,cAAc,WAAW,SAAS,YAAY;QACpD,MAAM,eAAe,WAAW,SAAS,aAAa;QAEtD,IAAI,MAAM,gBAAgB,eAAe,GAAG;YAC1C,OAAO;QACT;QAEA,IAAI,MAAM,eAAe;YACvB,OAAO;QACT;QAEA,IAAI,eAAe,aAAa;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,4DAA4D;YAC5D,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY;YAExD,2BAA2B;YAC3B,MAAM,aAAkB;gBACtB,4BAA4B;gBAC5B,mBAAmB,SAAS,iBAAiB,CAAC,IAAI,MAAM;gBACxD,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,UAAU,SAAS,QAAQ,IAAI;YACjC;YAEA,kDAAkD;YAClD,IAAI,gBAAgB;gBAClB,WAAW,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW;gBACxD,WAAW,SAAS,GAAG,SAAS,SAAS,CAAC,IAAI,GAAG,WAAW;gBAC5D,WAAW,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI;gBAC1C,WAAW,YAAY,GAAG,WAAW,SAAS,YAAY;gBAC1D,WAAW,YAAY,GAAG,IAAI,4CAA4C;;gBAC1E,WAAW,cAAc,GAAG,SAAS,cAAc;YACrD;YAEA,oEAAoE;YAEpE,kEAAkE;YAClE,MAAM,eAAe,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAEpD,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,YAAY,cAAc;YAE1D,UAAU,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,wBAAwB,CAAC;YACpE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,8CAA8C;IAC9C,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY;IAEnD,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,8MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;gCACX,OAAO,aAAa;;;;;;;sCAEvC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,CAAC,2BACA,6LAAC;gCAAK,WAAU;;oCAA0B;oCACC,OAAO,YAAY;oCAAC;;;;;;;;;;;;;;;;;;8BAMrE,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;8DAGhD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;oDACV,UAAU,WAAW,CAAC;oDACtB,OAAO,CAAC,YAAY,8CAA8C;;;;;;;;;;;;sDAKtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAa;;;;;;8DAGlD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,UAAU,WAAW,CAAC;oDACtB,OAAO,CAAC,YAAY,8CAA8C;;;;;;;;;;;;sDAKtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;8DAGhD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;oDACV,UAAU,WAAW,CAAC;oDACtB,OAAO,CAAC,YAAY,8CAA8C;;;;;;;;;;;;sDAKtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAe,WAAU;8DAAa;;;;;;8DAGrD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,OAAO,SAAS,YAAY;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAU;oDACV,UAAU,WAAW,CAAC;oDACtB,OAAO,CAAC,YAAY,8CAA8C;;;;;;;;;;;;sDAKtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAW,WAAU;8DAAa;;;;;;8DAGjD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAa;;;;;;8DAG9B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;;gEAAQ,SAAS,aAAa;gEAAC;;;;;;;;;;;;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAoB,WAAU;8DAAa;;;;;;8DAG1D,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAa;;;;;;8DAGlD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAa;;;;;;8DAG9C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;wBAOjB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GApVwB;KAAA", "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/EliminaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { <PERSON>ader2, <PERSON>ert<PERSON><PERSON>gle, Trash2 } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\n\nimport { ParcoCavo } from '@/types'\nimport { canModifyReel, getReelStateDescription } from '@/utils/bobineUtils'\n\ninterface EliminaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function EliminaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}: EliminaBobinaDialogProps) {\n  const [loading, setLoading] = useState(false)\n\n  const handleDelete = async () => {\n    if (!bobina) return\n\n    try {\n      setLoading(true)\n\n      if (!cantiereId || cantiereId <= 0) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})\n      const bobinaNumero = bobina.id_bobina.split('_B')[1]\n      \n      const response = await parcoCaviApi.deleteBobina(cantiereId, bobinaNumero)\n\n      let message = `Bobina ${bobina.numero_bobina} eliminata con successo`\n      \n      // Se è l'ultima bobina, aggiungi informazione aggiuntiva\n      if (response.data?.is_last_bobina) {\n        message += '. Era l\\'ultima bobina del cantiere.'\n      }\n\n      onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nell\\'eliminazione bobina:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'eliminazione della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  // Verifica se la bobina può essere eliminata\n  const canDelete = canModifyReel(bobina.stato_bobina) && \n                   bobina.metri_residui === bobina.metri_totali\n\n  // Determina il tipo di avviso da mostrare\n  const getWarningInfo = () => {\n    if (!canModifyReel(bobina.stato_bobina)) {\n      return {\n        type: 'error' as const,\n        title: 'Eliminazione non consentita',\n        message: `La bobina è in stato \"${bobina.stato_bobina}\" e non può essere eliminata. ${getReelStateDescription(bobina.stato_bobina)}`\n      }\n    }\n\n    if (bobina.metri_residui !== bobina.metri_totali) {\n      return {\n        type: 'error' as const,\n        title: 'Bobina in uso',\n        message: `La bobina ha ${bobina.metri_residui}m residui su ${bobina.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`\n      }\n    }\n\n    return {\n      type: 'warning' as const,\n      title: 'Conferma eliminazione',\n      message: 'Questa operazione è irreversibile. La bobina verrà rimossa definitivamente dal parco cavi.'\n    }\n  }\n\n  const warningInfo = getWarningInfo()\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Trash2 className=\"h-5 w-5 text-red-600\" />\n            Elimina Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Stai per eliminare la bobina {bobina.numero_bobina}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"py-4\">\n          {/* Informazioni bobina */}\n          <div className=\"bg-slate-50 p-4 rounded-lg mb-4\">\n            <h4 className=\"font-medium mb-2\">Dettagli bobina:</h4>\n            <div className=\"text-sm space-y-1\">\n              <div><strong>Bobina:</strong> {bobina.numero_bobina}</div>\n              <div><strong>Utility:</strong> {bobina.utility}</div>\n              <div><strong>Tipologia:</strong> {bobina.tipologia}</div>\n              <div><strong>Sezione:</strong> {bobina.sezione}</div>\n              <div><strong>Stato:</strong> {bobina.stato_bobina}</div>\n              <div><strong>Metri:</strong> {bobina.metri_residui}m / {bobina.metri_totali}m</div>\n              {bobina.ubicazione_bobina && (\n                <div><strong>Ubicazione:</strong> {bobina.ubicazione_bobina}</div>\n              )}\n            </div>\n          </div>\n\n          {/* Avviso */}\n          <Alert variant={warningInfo.type === 'error' ? 'destructive' : 'default'}>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <div>\n              <div className=\"font-medium\">{warningInfo.title}</div>\n              <AlertDescription className=\"mt-1\">\n                {warningInfo.message}\n              </AlertDescription>\n            </div>\n          </Alert>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            variant=\"destructive\" \n            onClick={handleDelete} \n            disabled={loading || !canDelete}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Eliminando...' : 'Elimina Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AACA;AAGA;;;AAjBA;;;;;;;;AA4Be,SAAS,oBAAoB,EAC1C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACkB;;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,kEAAkE;YAClE,MAAM,eAAe,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAEpD,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,YAAY;YAE7D,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC,uBAAuB,CAAC;YAErE,yDAAyD;YACzD,IAAI,SAAS,IAAI,EAAE,gBAAgB;gBACjC,WAAW;YACb;YAEA,UAAU;YACV;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,6CAA6C;IAC7C,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,KAClC,OAAO,aAAa,KAAK,OAAO,YAAY;IAE7D,0CAA0C;IAC1C,MAAM,iBAAiB;QACrB,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,GAAG;YACvC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,sBAAsB,EAAE,OAAO,YAAY,CAAC,8BAA8B,EAAE,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY,GAAG;YACtI;QACF;QAEA,IAAI,OAAO,aAAa,KAAK,OAAO,YAAY,EAAE;YAChD,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC,4EAA4E,CAAC;YAChK;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAG7C,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACa,OAAO,aAAa;;;;;;;;;;;;;8BAItD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAgB;gDAAE,OAAO,aAAa;;;;;;;sDACnD,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAmB;gDAAE,OAAO,SAAS;;;;;;;sDAClD,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,YAAY;;;;;;;sDACjD,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,aAAa;gDAAC;gDAAK,OAAO,YAAY;gDAAC;;;;;;;wCAC3E,OAAO,iBAAiB,kBACvB,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAoB;gDAAE,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;sCAMjE,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAS,YAAY,IAAI,KAAK,UAAU,gBAAgB;;8CAC7D,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAe,YAAY,KAAK;;;;;;sDAC/C,6LAAC,oIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDACzB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,WAAW,CAAC;;gCAErB,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GA5IwB;KAAA", "debugId": null}}, {"offset": {"line": 2647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/BobineStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Package, \n  CheckCircle, \n  Clock, \n  AlertTriangle,\n  AlertCircle,\n  BarChart3\n} from 'lucide-react'\n\ninterface Bob<PERSON> {\n  id_bobina: string\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  sezione: string\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  ubicazione_bobina?: string\n  fornitore?: string\n  n_DDT?: string\n  data_DDT?: string\n  configurazione?: string\n}\n\ninterface BobineStatisticsProps {\n  bobine: Bobina[]\n  filteredBobine: Bobina[]\n  className?: string\n}\n\nexport default function BobineStatistics({\n  bobine,\n  filteredBobine,\n  className\n}: BobineStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalBobine = bobine.length\n    const filteredCount = filteredBobine.length\n    \n    // Stati bobine\n    const disponibili = filteredBobine.filter(b => \n      b.stato_bobina === 'Disponibile'\n    ).length\n    \n    const inUso = filteredBobine.filter(b => \n      b.stato_bobina === 'In uso'\n    ).length\n    \n    const terminate = filteredBobine.filter(b => \n      b.stato_bobina === 'Terminata'\n    ).length\n    \n    const over = filteredBobine.filter(b => \n      b.stato_bobina === 'Over'\n    ).length\n    \n    // Calcoli metrature\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0)\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0)\n    const metriUtilizzati = metriTotali - metriResidui\n    \n    // Percentuale utilizzo\n    const percentualeUtilizzo = metriTotali > 0 ? Math.round((metriUtilizzati / metriTotali) * 100) : 0\n    \n    return {\n      totalBobine,\n      filteredCount,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    }\n  }, [bobine, filteredBobine])\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Bobine</span>\n          </div>\n        </div>\n\n        {/* Statistics distributed across full width */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2\">\n\n          {/* Total bobine */}\n          <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n            <Package className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <div>\n              <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n              <div className=\"text-xs text-mariner-600\">di {stats.totalBobine} bobine</div>\n            </div>\n          </div>\n\n          {/* Disponibili */}\n          <div className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\">\n            <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n            <div>\n              <div className=\"font-bold text-green-700 text-sm\">{stats.disponibili}</div>\n              <div className=\"text-xs text-green-600\">disponibili</div>\n            </div>\n          </div>\n\n          {/* In uso */}\n          <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\">\n            <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n            <div>\n              <div className=\"font-bold text-yellow-700 text-sm\">{stats.inUso}</div>\n              <div className=\"text-xs text-yellow-600\">in uso</div>\n            </div>\n          </div>\n\n          {/* Terminate */}\n          <div className=\"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg\">\n            <AlertTriangle className=\"h-3.5 w-3.5 text-red-600\" />\n            <div>\n              <div className=\"font-bold text-red-700 text-sm\">{stats.terminate}</div>\n              <div className=\"text-xs text-red-600\">terminate</div>\n            </div>\n          </div>\n\n          {/* Over */}\n          <div className=\"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg\">\n            <AlertCircle className=\"h-3.5 w-3.5 text-red-600\" />\n            <div>\n              <div className=\"font-bold text-red-700 text-sm\">{stats.over}</div>\n              <div className=\"text-xs text-red-600\">over</div>\n            </div>\n          </div>\n\n          {/* Meters progress */}\n          <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n            <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n              <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n            </div>\n            <div>\n              <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriUtilizzati.toLocaleString()}m</div>\n              <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n            </div>\n          </div>\n\n        </div>\n\n        {/* Utilizzo Progress bar - Dynamic colors */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>Utilizzo Complessivo Bobine</span>\n              <span className={`font-bold ${\n                stats.percentualeUtilizzo >= 80 ? 'text-red-700' :\n                stats.percentualeUtilizzo >= 60 ? 'text-orange-700' :\n                stats.percentualeUtilizzo >= 40 ? 'text-yellow-700' : 'text-green-700'\n              }`}>\n                {stats.percentualeUtilizzo}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeUtilizzo >= 80 ? 'bg-gradient-to-r from-red-500 to-red-600' :\n                  stats.percentualeUtilizzo >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  stats.percentualeUtilizzo >= 40 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  'bg-gradient-to-r from-green-500 to-green-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeUtilizzo, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Metri utilizzati vs totali disponibili</span>\n              <span>{stats.metriResidui.toLocaleString()}m residui</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAoCe,SAAS,iBAAiB,EACvC,MAAM,EACN,cAAc,EACd,SAAS,EACa;;IACtB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YACpB,MAAM,cAAc,OAAO,MAAM;YACjC,MAAM,gBAAgB,eAAe,MAAM;YAE3C,eAAe;YACf,MAAM,cAAc,eAAe,MAAM;mDAAC,CAAA,IACxC,EAAE,YAAY,KAAK;kDACnB,MAAM;YAER,MAAM,QAAQ,eAAe,MAAM;mDAAC,CAAA,IAClC,EAAE,YAAY,KAAK;kDACnB,MAAM;YAER,MAAM,YAAY,eAAe,MAAM;mDAAC,CAAA,IACtC,EAAE,YAAY,KAAK;kDACnB,MAAM;YA<PERSON>,MAAM,OAAO,eAAe,MAAM;mDAAC,CAAA,IACjC,EAAE,YAAY,KAAK;kDACnB,MAAM;YAER,oBAAoB;YACpB,MAAM,cAAc,eAAe,MAAM;+DAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC;8DAAG;YACnF,MAAM,eAAe,eAAe,MAAM;gEAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC;+DAAG;YACrF,MAAM,kBAAkB,cAAc;YAEtC,uBAAuB;YACvB,MAAM,sBAAsB,cAAc,IAAI,KAAK,KAAK,CAAC,AAAC,kBAAkB,cAAe,OAAO;YAElG,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;0CAAG;QAAC;QAAQ;KAAe;IAE3B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAK,WAAU;0CAAyC;;;;;;;;;;;;;;;;;8BAK7D,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAsC,MAAM,aAAa;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;gDAA2B;gDAAI,MAAM,WAAW;gDAAC;;;;;;;;;;;;;;;;;;;sCAKpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAoC,MAAM,WAAW;;;;;;sDACpE,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAqC,MAAM,KAAK;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAkC,MAAM,SAAS;;;;;;sDAChE,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAkC,MAAM,IAAI;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;gDAAqC,MAAM,eAAe,CAAC,cAAc;gDAAG;;;;;;;sDAC3F,6LAAC;4CAAI,WAAU;;gDAA0B;gDAAI,MAAM,WAAW,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;gBAOrF,MAAM,aAAa,GAAG,mBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,mBAAmB,IAAI,KAAK,iBAClC,MAAM,mBAAmB,IAAI,KAAK,oBAClC,MAAM,mBAAmB,IAAI,KAAK,oBAAoB,kBACtD;;wCACC,MAAM,mBAAmB;wCAAC;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,mBAAmB,IAAI,KAAK,6CAClC,MAAM,mBAAmB,IAAI,KAAK,mDAClC,MAAM,mBAAmB,IAAI,KAAK,mDAClC,gDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,mBAAmB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;wCAAM,MAAM,YAAY,CAAC,cAAc;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD;GAzJwB;KAAA", "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/parco-cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { parcoCaviApi } from '@/lib/api'\nimport { ParcoCavo } from '@/types'\nimport {\n  REEL_STATES,\n  getReelStateColor,\n  getReelRowColor,\n  determineReelState,\n  calculateReelUsagePercentage,\n  formatMeters,\n  getReelStateDescription\n} from '@/utils/bobineUtils'\nimport CreaBobinaDialog from '@/components/bobine/CreaBobinaDialog'\nimport ModificaBobinaDialog from '@/components/bobine/ModificaBobinaDialog'\nimport EliminaBobinaDialog from '@/components/bobine/EliminaBobinaDialog'\nimport BobineStatistics from '@/components/bobine/BobineStatistics'\nimport {\n  Package,\n  Search,\n  Plus,\n  Edit,\n  Trash2,\n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Download,\n  Upload,\n  Loader2,\n  Cable\n} from 'lucide-react'\n\nexport default function ParcoCaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [bobine, setBobine] = useState<ParcoCavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere, isLoading: authLoading } = useAuth()\n\n  // Get cantiere ID con fallback al localStorage come nella pagina cavi\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n      console.log('ParcoCavi - Cantiere ID impostato:', storedId)\n    }\n  }, [cantiere])\n\n  // Stati per i dialoghi\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\n  const [showAddCavoDialog, setShowAddCavoDialog] = useState(false)\n  const [selectedBobina, setSelectedBobina] = useState<ParcoCavo | null>(null)\n\n  // Stati per notifiche\n  const [successMessage, setSuccessMessage] = useState('')\n  const [errorMessage, setErrorMessage] = useState('')\n\n  // Carica le bobine dal backend\n  useEffect(() => {\n    if (cantiereId && cantiereId > 0) {\n      loadBobine()\n    } else if (!authLoading) {\n      // Solo se non stiamo caricando, mostra l'errore\n      setError('Cantiere non selezionato. Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.')\n      setBobine([])\n    }\n  }, [cantiereId, authLoading])\n\n  const loadBobine = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      console.log('🔍 Tentativo caricamento bobine per cantiere:', cantiereId)\n\n      // CONTROLLO: Il cantiere DEVE essere selezionato\n      if (!cantiereId || cantiereId <= 0) {\n        setError('Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine.')\n        setBobine([]) // Svuota la lista per sicurezza\n        return\n      }\n\n      const data = await parcoCaviApi.getBobine(cantiereId)\n      console.log('✅ Bobine caricate:', data?.length || 0)\n      setBobine(data || [])\n    } catch (error: any) {\n      console.error('❌ Errore caricamento bobine:', error)\n      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')\n      setBobine([]) // Svuota la lista in caso di errore\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // I filtri vengono applicati solo lato client per sicurezza\n  // Non ricarichiamo dal server quando cambiano i filtri\n\n  // Gestione notifiche\n  useEffect(() => {\n    if (successMessage) {\n      const timer = setTimeout(() => setSuccessMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [successMessage])\n\n  useEffect(() => {\n    if (errorMessage) {\n      const timer = setTimeout(() => setErrorMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [errorMessage])\n\n  // Funzioni per gestire i dialoghi\n  const handleAddCavoToBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowAddCavoDialog(true)\n  }\n\n  const handleEditBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowEditDialog(true)\n  }\n\n  const handleDeleteBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowDeleteDialog(true)\n  }\n\n  const handleCreateSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleCreateError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleEditSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleEditError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleDeleteSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleDeleteError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {\n    // Determina lo stato effettivo della bobina\n    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)\n    const colorClass = getReelStateColor(statoEffettivo)\n\n    return (\n      <Badge\n        className={colorClass}\n        title={getReelStateDescription(statoEffettivo)}\n      >\n        {statoEffettivo}\n      </Badge>\n    )\n  }\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      // Determina lo stato effettivo della bobina\n      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n\n      switch (selectedStatus) {\n        case 'disponibile':\n          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE\n          break\n        case 'in_uso':\n          matchesStatus = statoEffettivo === REEL_STATES.IN_USO\n          break\n        case 'esaurita':\n          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA\n          break\n        case 'over':\n          matchesStatus = statoEffettivo === REEL_STATES.OVER\n          break\n      }\n    }\n\n    return matchesSearch && matchesStatus\n  })\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n        {/* Avviso cantiere non selezionato */}\n        {(!cantiereId || cantiereId <= 0) && !authLoading && (\n          <Alert variant=\"destructive\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>\n              <strong>Attenzione:</strong> Nessun cantiere selezionato.\n              Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.\n            </AlertDescription>\n          </Alert>\n        )}\n        \n        {/* Action buttons */}\n        <div className=\"flex justify-end gap-2 mb-6\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            disabled={!cantiereId || cantiereId <= 0}\n            title={(!cantiereId || cantiereId <= 0) ? 'Seleziona un cantiere per esportare' : 'Esporta bobine'}\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            Esporta\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            disabled={!cantiereId || cantiereId <= 0}\n            title={(!cantiereId || cantiereId <= 0) ? 'Seleziona un cantiere per importare' : 'Importa bobine'}\n          >\n            <Upload className=\"h-4 w-4 mr-2\" />\n            Importa\n          </Button>\n          <Button\n            size=\"sm\"\n            onClick={() => setShowCreateDialog(true)}\n            disabled={!cantiereId || cantiereId <= 0}\n            title={(!cantiereId || cantiereId <= 0) ? 'Seleziona un cantiere per creare una bobina' : 'Crea nuova bobina'}\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Nuova Bobina\n          </Button>\n        </div>\n\n        {/* Statistics */}\n        <BobineStatistics\n          bobine={bobine}\n          filteredBobine={filteredBobine}\n          className=\"mb-6\"\n        />\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per bobina, tipologia o utility...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'disponibile', 'in_uso', 'esaurita', 'over'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutte' :\n                     status === 'disponibile' ? 'Disponibili' :\n                     status === 'in_uso' ? 'In Uso' :\n                     status === 'esaurita' ? 'Esaurite' : 'Over'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Bobine Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>\n            <CardDescription>\n              Gestione completa delle bobine con stato utilizzo e metrature\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Bobina</TableHead>\n                    <TableHead>Utility</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Formazione</TableHead>\n                    <TableHead>Metrature</TableHead>\n                    <TableHead>Utilizzo</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Ubicazione</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento bobine...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredBobine.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8 text-slate-500\">\n                        Nessuna bobina trovata\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredBobine.map((bobina) => {\n                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)\n                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n                      const rowColorClass = getReelRowColor(statoEffettivo)\n\n                      return (\n                        <TableRow\n                          key={bobina.id_bobina}\n                          className={`transition-colors ${rowColorClass}`}\n                        >\n                          <TableCell className=\"font-medium\">{bobina.numero_bobina || '-'}</TableCell>\n                          <TableCell>{bobina.utility || '-'}</TableCell>\n                          <TableCell>{bobina.tipologia || '-'}</TableCell>\n                          <TableCell>\n                            <div className=\"text-sm font-medium\">\n                              {bobina.sezione || '-'}\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>Residui: <span className=\"font-medium\">{formatMeters(bobina.metri_residui)}</span></div>\n                              <div className=\"text-slate-500\">Totali: {formatMeters(bobina.metri_totali)}</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"space-y-1\">\n                              <div className=\"text-sm font-medium\">{Math.round(percentualeUtilizzo)}%</div>\n                              <Progress value={percentualeUtilizzo} className=\"h-2\" />\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}\n                          </TableCell>\n                          <TableCell>\n                            <Badge variant=\"outline\">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-1\">\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleAddCavoToBobina(bobina)}\n                                title=\"Aggiungi cavo a bobina\"\n                              >\n                                <Cable className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleEditBobina(bobina)}\n                                title=\"Modifica bobina\"\n                              >\n                                <Edit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleDeleteBobina(bobina)}\n                                title=\"Elimina bobina\"\n                              >\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      )\n                    })\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Notifiche */}\n        {successMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert className=\"bg-green-50 border-green-200\">\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\n              <AlertDescription className=\"text-green-800\">\n                {successMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                {errorMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n      </div>\n\n      {/* Dialoghi */}\n      <CreaBobinaDialog\n        open={showCreateDialog}\n        onClose={() => setShowCreateDialog(false)}\n        cantiereId={cantiereId}\n        onSuccess={handleCreateSuccess}\n        onError={handleCreateError}\n      />\n\n      <ModificaBobinaDialog\n        open={showEditDialog}\n        onClose={() => setShowEditDialog(false)}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={handleEditSuccess}\n        onError={handleEditError}\n      />\n\n      <EliminaBobinaDialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={handleDeleteSuccess}\n        onError={handleDeleteError}\n      />\n\n      {/* TODO: Implementare AggiungiCavoDialog */}\n      {showAddCavoDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-6 rounded-lg\">\n            <h3 className=\"text-lg font-semibold mb-4\">Aggiungi Cavo a Bobina</h3>\n            <p className=\"mb-4\">Funzione in sviluppo per bobina: {selectedBobina?.numero_bobina}</p>\n            <Button onClick={() => setShowAddCavoDialog(false)}>Chiudi</Button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA1BA;;;;;;;;;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEzD,sEAAsE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;gBACd,QAAQ,GAAG,CAAC,sCAAsC;YACpD;QACF;kCAAG;QAAC;KAAS;IAEb,uBAAuB;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvE,sBAAsB;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,cAAc,aAAa,GAAG;gBAChC;YACF,OAAO,IAAI,CAAC,aAAa;gBACvB,gDAAgD;gBAChD,SAAS;gBACT,UAAU,EAAE;YACd;QACF;kCAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,SAAS;YAET,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,iDAAiD;YACjD,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,SAAS;gBACT,UAAU,EAAE,EAAE,gCAAgC;;gBAC9C;YACF;YAEA,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAC1C,QAAQ,GAAG,CAAC,sBAAsB,MAAM,UAAU;YAClD,UAAU,QAAQ,EAAE;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;YACzC,UAAU,EAAE,EAAE,oCAAoC;;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,4DAA4D;IAC5D,uDAAuD;IAEvD,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,gBAAgB;gBAClB,MAAM,QAAQ;qDAAW,IAAM,kBAAkB;oDAAK;gBACtD;+CAAO,IAAM,aAAa;;YAC5B;QACF;kCAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,cAAc;gBAChB,MAAM,QAAQ;qDAAW,IAAM,gBAAgB;oDAAK;gBACpD;+CAAO,IAAM,aAAa;;YAC5B;QACF;kCAAG;QAAC;KAAa;IAEjB,kCAAkC;IAClC,MAAM,wBAAwB,CAAC;QAC7B,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC,OAAe,eAAuB;QAC5D,4CAA4C;QAC5C,MAAM,iBAAiB,SAAS,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;QAClE,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;QAErC,qBACE,6LAAC,oIAAA,CAAA,QAAK;YACJ,WAAW;YACX,OAAO,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE;sBAE9B;;;;;;IAGP;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACpE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,OAAO,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAElF,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,4CAA4C;YAC5C,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;YAE1G,OAAQ;gBACN,KAAK;oBACH,gBAAgB,mBAAmB,8HAAA,CAAA,cAAW,CAAC,WAAW;oBAC1D;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,8HAAA,CAAA,cAAW,CAAC,MAAM;oBACrD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,8HAAA,CAAA,cAAW,CAAC,SAAS;oBACxD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,8HAAA,CAAA,cAAW,CAAC,IAAI;oBACnD;YACJ;QACF;QAEA,OAAO,iBAAiB;IAC1B;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAGZ,CAAC,CAAC,cAAc,cAAc,CAAC,KAAK,CAAC,6BACpC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;;kDACf,6LAAC;kDAAO;;;;;;oCAAoB;;;;;;;;;;;;;kCAOlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU,CAAC,cAAc,cAAc;gCACvC,OAAO,AAAC,CAAC,cAAc,cAAc,IAAK,wCAAwC;;kDAElF,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU,CAAC,cAAc,cAAc;gCACvC,OAAO,AAAC,CAAC,cAAc,cAAc,IAAK,wCAAwC;;kDAElF,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,oBAAoB;gCACnC,UAAU,CAAC,cAAc,cAAc;gCACvC,OAAO,AAAC,CAAC,cAAc,cAAc,IAAK,gDAAgD;;kDAE1F,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,6LAAC,mJAAA,CAAA,UAAgB;wBACf,QAAQ;wBACR,gBAAgB;wBAChB,WAAU;;;;;;kCAIZ,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAO;gDAAe;gDAAU;gDAAY;6CAAO,CAAC,GAAG,CAAC,CAAC,uBACzD,6LAAC,qIAAA,CAAA,SAAM;oDAEL,SAAS,mBAAmB,SAAS,YAAY;oDACjD,MAAK;oDACL,SAAS,IAAM,kBAAkB;8DAEhC,WAAW,QAAQ,UACnB,WAAW,gBAAgB,gBAC3B,WAAW,WAAW,WACtB,WAAW,aAAa,aAAa;mDARjC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBjB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;;4CAAC;4CAAgB,eAAe,MAAM;4CAAC;;;;;;;kDACjD,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0DACJ,6LAAC,oIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;;;;;;0DAGf,6LAAC,oIAAA,CAAA,YAAS;0DACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;8DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAG,WAAU;kEAC/B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAyB;;;;;;;;;;;;;;;;2DAKhD,sBACF,6LAAC,oIAAA,CAAA,WAAQ;8DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAG,WAAU;kEAC/B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB;;;;;;;;;;;;;;;;2DAIL,eAAe,MAAM,KAAK,kBAC5B,6LAAC,oIAAA,CAAA,WAAQ;8DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wDAAC,SAAS;wDAAG,WAAU;kEAAkC;;;;;;;;;;2DAKrE,eAAe,GAAG,CAAC,CAAC;oDAClB,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;oDAClG,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;oDAC1G,MAAM,gBAAgB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;oDAEtC,qBACE,6LAAC,oIAAA,CAAA,WAAQ;wDAEP,WAAW,CAAC,kBAAkB,EAAE,eAAe;;0EAE/C,6LAAC,oIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAe,OAAO,aAAa,IAAI;;;;;;0EAC5D,6LAAC,oIAAA,CAAA,YAAS;0EAAE,OAAO,OAAO,IAAI;;;;;;0EAC9B,6LAAC,oIAAA,CAAA,YAAS;0EAAE,OAAO,SAAS,IAAI;;;;;;0EAChC,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;8EACZ,OAAO,OAAO,IAAI;;;;;;;;;;;0EAGvB,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAI;8FAAS,6LAAC;oFAAK,WAAU;8FAAe,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;sFAC9E,6LAAC;4EAAI,WAAU;;gFAAiB;gFAAS,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;;;;;;;;;;;;;;;;;;0EAG7E,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;gFAAuB,KAAK,KAAK,CAAC;gFAAqB;;;;;;;sFACtE,6LAAC,uIAAA,CAAA,WAAQ;4EAAC,OAAO;4EAAqB,WAAU;;;;;;;;;;;;;;;;;0EAGpD,6LAAC,oIAAA,CAAA,YAAS;0EACP,eAAe,OAAO,YAAY,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;;;;;;0EAEhF,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,OAAO,iBAAiB,IAAI;;;;;;;;;;;0EAExD,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,sBAAsB;4EACrC,OAAM;sFAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;sFAEnB,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,iBAAiB;4EAChC,OAAM;sFAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,mBAAmB;4EAClC,OAAM;sFAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uDArDnB,OAAO,SAAS;;;;;gDA2D3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASX,gCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;;;;;;oBAMR,8BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CACd;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,mJAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAGX,6LAAC,uJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAGX,6LAAC,sJAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;YAIV,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;;gCAAO;gCAAkC,gBAAgB;;;;;;;sCACtE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,qBAAqB;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMhE;GArcwB;;QAO6B,kIAAA,CAAA,UAAO;;;KAPpC", "debugId": null}}]}