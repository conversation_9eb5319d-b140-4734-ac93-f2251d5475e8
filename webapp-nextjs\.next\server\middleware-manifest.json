{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "20c565bbf7d355395eaa1946f988eb35", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a3975f202e265554fe3bbf82452751b6fad57f80ebffa3ab31684993faf8b292", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "df95934bb96ea006c5f55f6f5331afc783105bfe08d2559d2366dcc8f486235b"}}}, "sortedMiddleware": ["/"], "functions": {}}