{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "fd21f2840651b7430ccd49b47f5f23cc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "73a41b6ed53be4d63d9fdb319178b51a106047cb09b475348b133eae70da55b9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7f090c6f17c93b43a45661e0804c693d439e14f3ba2f1cc8433221f3fe215c54"}}}, "sortedMiddleware": ["/"], "functions": {}}