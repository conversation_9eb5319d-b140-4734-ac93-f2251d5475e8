{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "fef6d799d086aa2a18869ec7959beac8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c53d8449fbd7e9dc125b4bfb1c7442a1e5d9dad98aec1bfa2052162cb45b19bf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "48abaf0455a5049be68c3176c7b1071f283b74a01afbb7138a0a8a1cfa438baa"}}}, "sortedMiddleware": ["/"], "functions": {}}