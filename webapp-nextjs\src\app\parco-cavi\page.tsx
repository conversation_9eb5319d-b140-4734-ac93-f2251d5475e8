'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { parcoCaviApi } from '@/lib/api'
import { ParcoCavo } from '@/types'
import {
  REEL_STATES,
  getReelStateColor,
  getReelRowColor,
  determineReelState,
  calculateReelUsagePercentage,
  formatMeters,
  getReelStateDescription
} from '@/utils/bobineUtils'
import CreaBobinaDialog from '@/components/bobine/CreaBobinaDialog'
import ModificaBobinaDialog from '@/components/bobine/ModificaBobinaDialog'
import EliminaBobinaDialog from '@/components/bobine/EliminaBobinaDialog'
import BobineStatistics from '@/components/bobine/BobineStatistics'
import {
  Package,
  Search,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  Loader2,
  Cable
} from 'lucide-react'

export default function ParcoCaviPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [bobine, setBobine] = useState<ParcoCavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere, isLoading: authLoading } = useAuth()

  // Debug temporaneo
  console.log('ParcoCavi - Cantiere:', cantiere)
  console.log('ParcoCavi - AuthLoading:', authLoading)
  console.log('ParcoCavi - User:', user)

  // Stati per i dialoghi
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showAddCavoDialog, setShowAddCavoDialog] = useState(false)
  const [selectedBobina, setSelectedBobina] = useState<ParcoCavo | null>(null)

  // Stati per notifiche
  const [successMessage, setSuccessMessage] = useState('')
  const [errorMessage, setErrorMessage] = useState('')

  // Carica le bobine dal backend
  useEffect(() => {
    if (cantiere?.id_cantiere) {
      loadBobine()
    } else if (!authLoading) {
      // Solo se non stiamo caricando, mostra l'errore
      setError('Cantiere non selezionato. Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.')
      setBobine([])
    }
  }, [cantiere?.id_cantiere, authLoading])

  const loadBobine = async () => {
    try {
      setIsLoading(true)
      setError('')

      // CONTROLLO CRITICO: Il cantiere DEVE essere selezionato
      if (!cantiere?.id_cantiere) {
        setError('Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine.')
        setBobine([]) // Svuota la lista per sicurezza
        return
      }

      const data = await parcoCaviApi.getBobine(cantiere.id_cantiere)
      setBobine(data || [])
    } catch (error: any) {
      console.error('Errore caricamento bobine:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')
      setBobine([]) // Svuota la lista in caso di errore
    } finally {
      setIsLoading(false)
    }
  }

  // I filtri vengono applicati solo lato client per sicurezza
  // Non ricarichiamo dal server quando cambiano i filtri

  // Gestione notifiche
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(''), 5000)
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(''), 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  // Funzioni per gestire i dialoghi
  const handleAddCavoToBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowAddCavoDialog(true)
  }

  const handleEditBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowEditDialog(true)
  }

  const handleDeleteBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowDeleteDialog(true)
  }

  const handleCreateSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleCreateError = (message: string) => {
    setErrorMessage(message)
  }

  const handleEditSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleEditError = (message: string) => {
    setErrorMessage(message)
  }

  const handleDeleteSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleDeleteError = (message: string) => {
    setErrorMessage(message)
  }

  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {
    // Determina lo stato effettivo della bobina
    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)
    const colorClass = getReelStateColor(statoEffettivo)

    return (
      <Badge
        className={colorClass}
        title={getReelStateDescription(statoEffettivo)}
      >
        {statoEffettivo}
      </Badge>
    )
  }

  const filteredBobine = bobine.filter(bobina => {
    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())

    let matchesStatus = true
    if (selectedStatus !== 'all') {
      // Determina lo stato effettivo della bobina
      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)

      switch (selectedStatus) {
        case 'disponibile':
          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE
          break
        case 'in_uso':
          matchesStatus = statoEffettivo === REEL_STATES.IN_USO
          break
        case 'esaurita':
          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA
          break
        case 'over':
          matchesStatus = statoEffettivo === REEL_STATES.OVER
          break
      }
    }

    return matchesSearch && matchesStatus
  })



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Avviso cantiere non selezionato */}
        {!cantiere?.id_cantiere && !authLoading && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Attenzione:</strong> Nessun cantiere selezionato.
              Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.
            </AlertDescription>
          </Alert>
        )}
        
        {/* Action buttons */}
        <div className="flex justify-end gap-2 mb-6">
          <Button
            variant="outline"
            size="sm"
            disabled={!cantiere?.id_cantiere}
            title={!cantiere?.id_cantiere ? 'Seleziona un cantiere per esportare' : 'Esporta bobine'}
          >
            <Download className="h-4 w-4 mr-2" />
            Esporta
          </Button>
          <Button
            variant="outline"
            size="sm"
            disabled={!cantiere?.id_cantiere}
            title={!cantiere?.id_cantiere ? 'Seleziona un cantiere per importare' : 'Importa bobine'}
          >
            <Upload className="h-4 w-4 mr-2" />
            Importa
          </Button>
          <Button
            size="sm"
            onClick={() => setShowCreateDialog(true)}
            disabled={!cantiere?.id_cantiere}
            title={!cantiere?.id_cantiere ? 'Seleziona un cantiere per creare una bobina' : 'Crea nuova bobina'}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nuova Bobina
          </Button>
        </div>

        {/* Statistics */}
        <BobineStatistics
          bobine={bobine}
          filteredBobine={filteredBobine}
          className="mb-6"
        />

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Ricerca e Filtri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Cerca per bobina, tipologia o utility..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'disponibile', 'in_uso', 'esaurita', 'over'].map((status) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'all' ? 'Tutte' :
                     status === 'disponibile' ? 'Disponibili' :
                     status === 'in_uso' ? 'In Uso' :
                     status === 'esaurita' ? 'Esaurite' : 'Over'}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bobine Table */}
        <Card>
          <CardHeader>
            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>
            <CardDescription>
              Gestione completa delle bobine con stato utilizzo e metrature
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Bobina</TableHead>
                    <TableHead>Utility</TableHead>
                    <TableHead>Tipologia</TableHead>
                    <TableHead>Formazione</TableHead>
                    <TableHead>Metrature</TableHead>
                    <TableHead>Utilizzo</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead>Ubicazione</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento bobine...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredBobine.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-slate-500">
                        Nessuna bobina trovata
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBobine.map((bobina) => {
                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)
                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)
                      const rowColorClass = getReelRowColor(statoEffettivo)

                      return (
                        <TableRow
                          key={bobina.id_bobina}
                          className={`transition-colors ${rowColorClass}`}
                        >
                          <TableCell className="font-medium">{bobina.numero_bobina || '-'}</TableCell>
                          <TableCell>{bobina.utility || '-'}</TableCell>
                          <TableCell>{bobina.tipologia || '-'}</TableCell>
                          <TableCell>
                            <div className="text-sm font-medium">
                              {bobina.sezione || '-'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>Residui: <span className="font-medium">{formatMeters(bobina.metri_residui)}</span></div>
                              <div className="text-slate-500">Totali: {formatMeters(bobina.metri_totali)}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm font-medium">{Math.round(percentualeUtilizzo)}%</div>
                              <Progress value={percentualeUtilizzo} className="h-2" />
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleAddCavoToBobina(bobina)}
                                title="Aggiungi cavo a bobina"
                              >
                                <Cable className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditBobina(bobina)}
                                title="Modifica bobina"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteBobina(bobina)}
                                title="Elimina bobina"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Notifiche */}
        {successMessage && (
          <div className="fixed top-4 right-4 z-50">
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                {successMessage}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {errorMessage && (
          <div className="fixed top-4 right-4 z-50">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessage}
              </AlertDescription>
            </Alert>
          </div>
        )}

      </div>

      {/* Dialoghi */}
      <CreaBobinaDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSuccess={handleCreateSuccess}
        onError={handleCreateError}
      />

      <ModificaBobinaDialog
        open={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        bobina={selectedBobina}
        onSuccess={handleEditSuccess}
        onError={handleEditError}
      />

      <EliminaBobinaDialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        bobina={selectedBobina}
        onSuccess={handleDeleteSuccess}
        onError={handleDeleteError}
      />

      {/* TODO: Implementare AggiungiCavoDialog */}
      {showAddCavoDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Aggiungi Cavo a Bobina</h3>
            <p className="mb-4">Funzione in sviluppo per bobina: {selectedBobina?.numero_bobina}</p>
            <Button onClick={() => setShowAddCavoDialog(false)}>Chiudi</Button>
          </div>
        </div>
      )}
    </div>
  )
}
