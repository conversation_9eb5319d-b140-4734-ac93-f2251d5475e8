'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/contexts/AuthContext'
import { parcoCaviApi } from '@/lib/api'
import { ParcoCavo } from '@/types'
import {
  REEL_STATES,
  getReelStateColor,
  getReelRowColor,
  determineReelState,
  calculateReelUsagePercentage,
  formatMeters,
  getReelStateDescription
} from '@/utils/bobineUtils'
import { 
  Package, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Upload,
  Loader2
} from 'lucide-react'

export default function ParcoCaviPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [bobine, setBobine] = useState<ParcoCavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica le bobine dal backend
  useEffect(() => {
    loadBobine()
  }, [])

  const loadBobine = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const data = await parcoCaviApi.getBobine(cantiereId)
      setBobine(data)
    } catch (error: any) {
      console.error('Errore caricamento bobine:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')
    } finally {
      setIsLoading(false)
    }
  }

  // Ricarica quando cambiano i filtri
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadBobine()
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm, selectedStatus])

  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {
    // Determina lo stato effettivo della bobina
    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)
    const colorClass = getReelStateColor(statoEffettivo)

    return (
      <Badge
        className={colorClass}
        title={getReelStateDescription(statoEffettivo)}
      >
        {statoEffettivo}
      </Badge>
    )
  }

  const filteredBobine = bobine.filter(bobina => {
    const matchesSearch = bobina.id_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())

    let matchesStatus = true
    if (selectedStatus !== 'all') {
      // Determina lo stato effettivo della bobina
      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)

      switch (selectedStatus) {
        case 'disponibile':
          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE
          break
        case 'in_uso':
          matchesStatus = statoEffettivo === REEL_STATES.IN_USO
          break
        case 'esaurita':
          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA
          break
        case 'over':
          matchesStatus = statoEffettivo === REEL_STATES.OVER
          break
      }
    }

    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: bobine.length,
    disponibili: bobine.filter(b => {
      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)
      return stato === REEL_STATES.DISPONIBILE
    }).length,
    in_uso: bobine.filter(b => {
      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)
      return stato === REEL_STATES.IN_USO
    }).length,
    esaurite: bobine.filter(b => {
      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)
      return stato === REEL_STATES.TERMINATA
    }).length,
    over: bobine.filter(b => {
      const stato = b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)
      return stato === REEL_STATES.OVER
    }).length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">
        
        {/* Action buttons */}
        <div className="flex justify-end gap-2 mb-6">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Esporta
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Importa
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Nuova Bobina
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Disponibili</p>
                  <p className="text-2xl font-bold text-green-600">{stats.disponibili}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Uso</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.in_uso}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Esaurite</p>
                  <p className="text-2xl font-bold text-red-600">{stats.esaurite}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Over</p>
                  <p className="text-2xl font-bold text-red-800">{stats.over}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-800" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Ricerca e Filtri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Cerca per ID bobina, numero, tipologia o utility..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'disponibile', 'in_uso', 'esaurita', 'over'].map((status) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'all' ? 'Tutte' :
                     status === 'disponibile' ? 'Disponibili' :
                     status === 'in_uso' ? 'In Uso' :
                     status === 'esaurita' ? 'Esaurite' : 'Over'}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bobine Table */}
        <Card>
          <CardHeader>
            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>
            <CardDescription>
              Gestione completa delle bobine con stato utilizzo e metrature
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Bobina</TableHead>
                    <TableHead>Numero</TableHead>
                    <TableHead>Utility</TableHead>
                    <TableHead>Tipologia</TableHead>
                    <TableHead>Conduttori/Sezione</TableHead>
                    <TableHead>Metrature</TableHead>
                    <TableHead>Utilizzo</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead>Ubicazione</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento bobine...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredBobine.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8 text-slate-500">
                        Nessuna bobina trovata
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBobine.map((bobina) => {
                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)
                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)
                      const rowColorClass = getReelRowColor(statoEffettivo)

                      return (
                        <TableRow
                          key={bobina.id_bobina}
                          className={`transition-colors ${rowColorClass}`}
                        >
                          <TableCell className="font-medium">{bobina.id_bobina}</TableCell>
                          <TableCell>{bobina.numero_bobina || '-'}</TableCell>
                          <TableCell>{bobina.utility || '-'}</TableCell>
                          <TableCell>{bobina.tipologia || '-'}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{bobina.n_conduttori || '-'}</div>
                              <div className="text-slate-500">{bobina.sezione || '-'}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>Residui: <span className="font-medium">{formatMeters(bobina.metri_residui)}</span></div>
                              <div className="text-slate-500">Totali: {formatMeters(bobina.metri_totali)}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm font-medium">{Math.round(percentualeUtilizzo)}%</div>
                              <Progress value={percentualeUtilizzo} className="h-2" />
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
